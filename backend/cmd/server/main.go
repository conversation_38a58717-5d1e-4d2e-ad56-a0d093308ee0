package main

import (
	"bdb-backend/internal/api"
	"bdb-backend/pkg/config"
	"bdb-backend/pkg/logger"
)

func main() {
	// 初始化配置
	cfg := config.Load()

	// 初始化日志
	logger.Init(cfg.<PERSON>, cfg.Server.Mode)

	// 使用新的全局日志记录器
	logger.Log.Info().Msg("服务器启动中...")

	// 使用 Wire 构建服务器实例
	server, err := api.BuildServer(cfg)
	if err != nil {
		logger.Log.Fatal().Err(err).Msg("服务器构建失败")
	}

	// 启动服务器
	if err := server.Run(); err != nil {
		logger.Log.Fatal().Err(err).Msg("启动服务器失败")
	}
}
