package response

import "github.com/gin-gonic/gin"

/*
响应包使用示例

1. 成功响应:
   response.OK(ctx, data)

2. 错误响应:
   response.Fail(ctx, "操作失败")

3. 分页响应:
   users := []User{...}
   response.OK(ctx, response.Paginated(users, total, page, pageSize))

4. 自定义错误响应:
   response.Unauthorized(ctx, "")           // 使用默认消息
   response.Forbidden(ctx, "自定义权限错误")    // 使用自定义消息
   response.BadRequest(ctx, "参数验证失败")

5. 服务器错误:
   response.ServerError(ctx, "数据库连接失败")

响应格式:
{
  "code": 0,
  "message": "操作成功",
  "data": {}
}

分页响应格式:
{
  "code": 0,
  "message": "操作成功",
  "data": {
    "list": [...],
    "total": 100,
    "page": 1,
    "pageSize": 10,
    "pages": 10
  }
}
*/

// ExampleController 示例控制器
func ExampleController() {
	// 这个函数仅用于文档说明，不会被实际调用
}

// ExampleUsage 使用示例
func ExampleUsage(ctx *gin.Context) {
	// 成功响应示例
	user := map[string]interface{}{
		"id":   1,
		"name": "张三",
	}
	OK(ctx, user)

	// 分页响应示例
	users := []map[string]interface{}{
		{"id": 1, "name": "张三"},
		{"id": 2, "name": "李四"},
	}
	paginatedData := Paginated(users, 100, 1, 10)
	OK(ctx, paginatedData)

	// 错误响应示例
	Fail(ctx, "用户不存在")
	Unauthorized(ctx, "")
	Forbidden(ctx, "无权限访问")
}
