package jwt

import (
	"errors"
	"fmt"
	"strconv"
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/pkg/config"

	"github.com/golang-jwt/jwt/v5"
)

// JWTService JWT服务接口
type JWTService interface {
	// GenerateUserToken 生成用户认证token
	GenerateUserToken(userID uint, deviceID string) (string, error)

	// ValidateUserToken 验证用户认证token
	ValidateUserToken(tokenString string) (*UserClaims, error)

	// GenerateCentrifugoToken 生成Centrifugo连接token
	GenerateCentrifugoToken(user *model.User, channels []string, ttl time.Duration) (string, error)

	// GenerateCentrifugoChannelToken 生成Centrifugo频道订阅token
	GenerateCentrifugoChannelToken(userID uint, channel string, ttl time.Duration) (string, error)

	// ValidateCentrifugoToken 验证Centrifugo token
	ValidateCentrifugoToken(tokenString string) (*CentrifugoClaims, error)

	// ExtractUserIDFromToken 从令牌字符串中提取用户ID
	ExtractUserIDFromToken(tokenString string) (uint, error)
}

// UserClaims 用户JWT声明
type UserClaims struct {
	UserID   uint   `json:"user_id"`
	DeviceID string `json:"device_id,omitempty"` // 设备ID，用于安全验证
	jwt.RegisteredClaims
}

// CentrifugoClaims Centrifugo JWT声明
type CentrifugoClaims struct {
	Sub      string                 `json:"sub"`      // 用户ID字符串
	Exp      int64                  `json:"exp"`      // 过期时间
	Iat      int64                  `json:"iat"`      // 签发时间
	Info     map[string]interface{} `json:"info"`     // 用户信息
	Channels []string               `json:"channels"` // 可访问的频道列表
	Channel  string                 `json:"channel"`  // 单个频道（用于频道订阅token）
	jwt.RegisteredClaims
}

// jwtService JWT服务实现
type jwtService struct {
	userSecret       []byte
	centrifugoSecret []byte
	userTokenTTL     time.Duration
}

// NewJWTService 创建JWT服务
func NewJWTService(cfg *config.Config) JWTService {
	return &jwtService{
		userSecret:       []byte(cfg.JWT.Secret),
		centrifugoSecret: []byte(cfg.Centrifugo.HMACSecret),
		userTokenTTL:     cfg.JWT.TTL,
	}
}

// GenerateUserToken 生成用户认证token
func (j *jwtService) GenerateUserToken(userID uint, deviceID string) (string, error) {
	now := time.Now()
	claims := &UserClaims{
		UserID:   userID,
		DeviceID: deviceID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(j.userTokenTTL)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "bdb-backend",
			Subject:   strconv.FormatUint(uint64(userID), 10),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.userSecret)
}

// ValidateUserToken 验证用户认证token
func (j *jwtService) ValidateUserToken(tokenString string) (*UserClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &UserClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.userSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*UserClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// GenerateCentrifugoToken 生成Centrifugo连接token
func (j *jwtService) GenerateCentrifugoToken(user *model.User, channels []string, ttl time.Duration) (string, error) {
	now := time.Now()

	// 构建用户信息
	userInfo := map[string]interface{}{
		"user_id":  user.ID,
		"nickname": user.Nickname,
		"avatar":   user.Avatar,
	}

	claims := &CentrifugoClaims{
		Sub:      strconv.FormatUint(uint64(user.ID), 10),
		Exp:      now.Add(ttl).Unix(),
		Iat:      now.Unix(),
		Info:     userInfo,
		Channels: channels,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(ttl)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "bdb-backend",
			Subject:   strconv.FormatUint(uint64(user.ID), 10),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.centrifugoSecret)
}

// GenerateCentrifugoChannelToken 生成Centrifugo频道订阅token
func (j *jwtService) GenerateCentrifugoChannelToken(userID uint, channel string, ttl time.Duration) (string, error) {
	now := time.Now()

	claims := &CentrifugoClaims{
		Sub:     strconv.FormatUint(uint64(userID), 10),
		Exp:     now.Add(ttl).Unix(),
		Iat:     now.Unix(),
		Channel: channel,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(now.Add(ttl)),
			IssuedAt:  jwt.NewNumericDate(now),
			NotBefore: jwt.NewNumericDate(now),
			Issuer:    "bdb-backend",
			Subject:   strconv.FormatUint(uint64(userID), 10),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(j.centrifugoSecret)
}

// ValidateCentrifugoToken 验证Centrifugo token
func (j *jwtService) ValidateCentrifugoToken(tokenString string) (*CentrifugoClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &CentrifugoClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.centrifugoSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*CentrifugoClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// ExtractUserIDFromToken 从令牌字符串中提取用户ID
func (j *jwtService) ExtractUserIDFromToken(tokenString string) (uint, error) {
	token, err := jwt.ParseWithClaims(tokenString, &UserClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return j.userSecret, nil
	})

	if err != nil {
		return 0, fmt.Errorf("token validation failed: %w", err)
	}

	if claims, ok := token.Claims.(*UserClaims); ok && token.Valid {
		userID, err := strconv.ParseUint(claims.Subject, 10, 64)
		if err != nil {
			return 0, fmt.Errorf("invalid user ID in token: %w", err)
		}
		return uint(userID), nil
	}

	return 0, errors.New("invalid token")
}

// IsTokenExpired 检查token是否过期
func IsTokenExpired(tokenString string, secret []byte) bool {
	token, err := jwt.ParseWithClaims(tokenString, &UserClaims{}, func(token *jwt.Token) (interface{}, error) {
		return secret, nil
	})

	if err != nil {
		return true
	}

	if claims, ok := token.Claims.(*UserClaims); ok {
		return claims.ExpiresAt.Time.Before(time.Now())
	}

	return true
}

// RefreshTokenIfNeeded 如果token即将过期，则刷新token
func RefreshTokenIfNeeded(tokenString string, secret []byte, threshold time.Duration, newTTL time.Duration) (string, bool, error) {
	token, err := jwt.ParseWithClaims(tokenString, &UserClaims{}, func(token *jwt.Token) (interface{}, error) {
		return secret, nil
	})

	if err != nil {
		return "", false, err
	}

	if claims, ok := token.Claims.(*UserClaims); ok && token.Valid {
		// 检查是否需要刷新
		if time.Until(claims.ExpiresAt.Time) < threshold {
			// 生成新token
			now := time.Now()
			newClaims := &UserClaims{
				UserID: claims.UserID,
				RegisteredClaims: jwt.RegisteredClaims{
					ExpiresAt: jwt.NewNumericDate(now.Add(newTTL)),
					IssuedAt:  jwt.NewNumericDate(now),
					NotBefore: jwt.NewNumericDate(now),
					Issuer:    "bdb-backend",
					Subject:   strconv.FormatUint(uint64(claims.UserID), 10),
				},
			}

			newToken := jwt.NewWithClaims(jwt.SigningMethodHS256, newClaims)
			newTokenString, err := newToken.SignedString(secret)
			if err != nil {
				return "", false, err
			}

			return newTokenString, true, nil
		}
	}

	return tokenString, false, nil
}
