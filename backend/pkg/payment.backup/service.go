package payment

import (
	"context"
	"errors"
	"fmt"

	"bdb-backend/pkg/wechat"

	"github.com/rs/zerolog/log"
)

// Service 统一支付服务接口
type Service interface {
	// CreateOrder 创建支付订单
	CreateOrder(ctx context.Context, req *CreateOrderRequest) (*CreateOrderResponse, error)
	// QueryOrder 查询订单状态
	QueryOrder(ctx context.Context, req *QueryOrderRequest) (*QueryOrderResponse, error)
	// Refund 申请退款
	Refund(ctx context.Context, req *RefundRequest) (*RefundResponse, error)
	// HandleNotify 处理支付回调
	HandleNotify(ctx context.Context, method PaymentMethod, data []byte) (*NotifyData, error)
}

type service struct {
	wechatPayment wechat.PaymentService
	// alipayPayment alipay.PaymentService // 未来可扩展支付宝支付
}

// NewService 创建统一支付服务
func NewService(wechatPayment wechat.PaymentService) Service {
	return &service{
		wechatPayment: wechatPayment,
	}
}

// CreateOrder 创建支付订单
func (s *service) CreateOrder(ctx context.Context, req *CreateOrderRequest) (*CreateOrderResponse, error) {
	if req == nil {
		return nil, errors.New("创建订单请求不能为空")
	}

	switch req.PaymentMethod {
	case PaymentMethodWechat:
		return s.createWechatOrder(ctx, req)
	case PaymentMethodAlipay:
		return nil, errors.New("支付宝支付暂未支持")
	default:
		return nil, fmt.Errorf("不支持的支付方式: %s", req.PaymentMethod)
	}
}

// QueryOrder 查询订单状态
func (s *service) QueryOrder(ctx context.Context, req *QueryOrderRequest) (*QueryOrderResponse, error) {
	if req == nil {
		return nil, errors.New("查询订单请求不能为空")
	}

	switch req.PaymentMethod {
	case PaymentMethodWechat:
		return s.queryWechatOrder(ctx, req)
	case PaymentMethodAlipay:
		return nil, errors.New("支付宝支付暂未支持")
	default:
		return nil, fmt.Errorf("不支持的支付方式: %s", req.PaymentMethod)
	}
}

// Refund 申请退款
func (s *service) Refund(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	if req == nil {
		return nil, errors.New("退款请求不能为空")
	}

	switch req.PaymentMethod {
	case PaymentMethodWechat:
		return s.refundWechatOrder(ctx, req)
	case PaymentMethodAlipay:
		return nil, errors.New("支付宝支付暂未支持")
	default:
		return nil, fmt.Errorf("不支持的支付方式: %s", req.PaymentMethod)
	}
}

// HandleNotify 处理支付回调
func (s *service) HandleNotify(ctx context.Context, method PaymentMethod, data []byte) (*NotifyData, error) {
	switch method {
	case PaymentMethodWechat:
		return s.handleWechatNotify(ctx, data)
	case PaymentMethodAlipay:
		return nil, errors.New("支付宝支付暂未支持")
	default:
		return nil, fmt.Errorf("不支持的支付方式: %s", method)
	}
}

// createWechatOrder 创建微信支付订单
func (s *service) createWechatOrder(ctx context.Context, req *CreateOrderRequest) (*CreateOrderResponse, error) {
	wechatReq := &wechat.PaymentOrderRequest{
		OutTradeNo:  req.OutTradeNo,
		Description: req.Description,
		Amount:      req.Amount,
		OpenID:      req.OpenID,
		NotifyURL:   req.NotifyURL,
		AttachData:  req.AttachData,
	}

	wechatResp, err := s.wechatPayment.CreateOrder(ctx, wechatReq)
	if err != nil {
		log.Error().Err(err).Str("out_trade_no", req.OutTradeNo).Msg("Failed to create wechat payment order")
		return nil, err
	}

	// 将微信支付响应转换为统一响应格式
	paymentData := map[string]interface{}{
		"prepay_id":  wechatResp.PrepayID,
		"package":    wechatResp.Package,
		"nonce_str":  wechatResp.NonceStr,
		"time_stamp": wechatResp.TimeStamp,
		"pay_sign":   wechatResp.PaySign,
		"sign_type":  wechatResp.SignType,
	}

	return &CreateOrderResponse{
		OutTradeNo:    req.OutTradeNo,
		PaymentMethod: PaymentMethodWechat,
		PaymentData:   paymentData,
		ExpireTime:    req.ExpireTime,
	}, nil
}

// queryWechatOrder 查询微信支付订单
func (s *service) queryWechatOrder(ctx context.Context, req *QueryOrderRequest) (*QueryOrderResponse, error) {
	wechatResp, err := s.wechatPayment.QueryOrder(ctx, req.OutTradeNo)
	if err != nil {
		log.Error().Err(err).Str("out_trade_no", req.OutTradeNo).Msg("Failed to query wechat payment order")
		return nil, err
	}

	// 将微信支付状态转换为统一状态
	var status PaymentStatus
	switch wechatResp.TradeState {
	case "SUCCESS":
		status = PaymentStatusPaid
	case "REFUND":
		status = PaymentStatusRefunded
	case "NOTPAY":
		status = PaymentStatusPending
	case "CLOSED":
		status = PaymentStatusCancelled
	case "REVOKED":
		status = PaymentStatusCancelled
	case "USERPAYING":
		status = PaymentStatusPending
	case "PAYERROR":
		status = PaymentStatusFailed
	default:
		status = PaymentStatusPending
	}

	return &QueryOrderResponse{
		OutTradeNo:    req.OutTradeNo,
		TransactionID: wechatResp.TransactionID,
		PaymentMethod: PaymentMethodWechat,
		Status:        status,
		Amount:        int64(wechatResp.Amount.Total),
		PaidAmount:    int64(wechatResp.Amount.PayerTotal),
		// PaidTime:      &wechatResp.SuccessTime,
		AttachData: wechatResp.Attach,
	}, nil
}

// refundWechatOrder 微信支付退款
func (s *service) refundWechatOrder(ctx context.Context, req *RefundRequest) (*RefundResponse, error) {
	// TODO: 实现微信支付退款逻辑
	log.Info().Str("out_trade_no", req.OutTradeNo).Str("out_refund_no", req.OutRefundNo).Msg("Wechat refund request")
	return nil, errors.New("微信支付退款功能待实现")
}

// handleWechatNotify 处理微信支付回调
func (s *service) handleWechatNotify(ctx context.Context, data []byte) (*NotifyData, error) {
	// TODO: 实现微信支付回调处理逻辑
	log.Info().Msg("Handling wechat payment notify")
	return nil, errors.New("微信支付回调处理功能待实现")
}
