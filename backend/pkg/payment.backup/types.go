package payment

import "time"

// PaymentMethod 支付方式
type PaymentMethod string

const (
	PaymentMethodWechat PaymentMethod = "wechat"
	PaymentMethodAlipay PaymentMethod = "alipay"
)

// PaymentStatus 支付状态
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "pending"   // 待支付
	PaymentStatusPaid      PaymentStatus = "paid"      // 已支付
	PaymentStatusCancelled PaymentStatus = "cancelled" // 已取消
	PaymentStatusRefunded  PaymentStatus = "refunded"  // 已退款
	PaymentStatusFailed    PaymentStatus = "failed"    // 支付失败
)

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	OutTradeNo    string        `json:"out_trade_no"`   // 商户订单号
	Description   string        `json:"description"`    // 商品描述
	Amount        int64         `json:"amount"`         // 订单金额(分)
	UserID        string        `json:"user_id"`        // 用户ID
	OpenID        string        `json:"openid"`         // 用户openid (微信支付需要)
	PaymentMethod PaymentMethod `json:"payment_method"` // 支付方式
	NotifyURL     string        `json:"notify_url"`     // 通知地址
	AttachData    string        `json:"attach"`         // 附加数据
	ExpireTime    *time.Time    `json:"expire_time"`    // 过期时间
}

// CreateOrderResponse 创建订单响应
type CreateOrderResponse struct {
	OutTradeNo    string                 `json:"out_trade_no"`   // 商户订单号
	PaymentMethod PaymentMethod          `json:"payment_method"` // 支付方式
	PaymentData   map[string]interface{} `json:"payment_data"`   // 支付数据(不同支付方式返回不同数据)
	ExpireTime    *time.Time             `json:"expire_time"`    // 过期时间
}

// QueryOrderRequest 查询订单请求
type QueryOrderRequest struct {
	OutTradeNo    string        `json:"out_trade_no"`   // 商户订单号
	PaymentMethod PaymentMethod `json:"payment_method"` // 支付方式
}

// QueryOrderResponse 查询订单响应
type QueryOrderResponse struct {
	OutTradeNo    string        `json:"out_trade_no"`   // 商户订单号
	TransactionID string        `json:"transaction_id"` // 第三方交易号
	PaymentMethod PaymentMethod `json:"payment_method"` // 支付方式
	Status        PaymentStatus `json:"status"`         // 支付状态
	Amount        int64         `json:"amount"`         // 订单金额(分)
	PaidAmount    int64         `json:"paid_amount"`    // 实际支付金额(分)
	PaidTime      *time.Time    `json:"paid_time"`      // 支付时间
	Description   string        `json:"description"`    // 商品描述
	AttachData    string        `json:"attach"`         // 附加数据
}

// RefundRequest 退款请求
type RefundRequest struct {
	OutTradeNo    string        `json:"out_trade_no"`   // 商户订单号
	OutRefundNo   string        `json:"out_refund_no"`  // 商户退款单号
	PaymentMethod PaymentMethod `json:"payment_method"` // 支付方式
	Amount        int64         `json:"amount"`         // 退款金额(分)
	Total         int64         `json:"total"`          // 原订单金额(分)
	Reason        string        `json:"reason"`         // 退款原因
	NotifyURL     string        `json:"notify_url"`     // 退款通知地址
}

// RefundResponse 退款响应
type RefundResponse struct {
	OutTradeNo    string        `json:"out_trade_no"`   // 商户订单号
	OutRefundNo   string        `json:"out_refund_no"`  // 商户退款单号
	RefundID      string        `json:"refund_id"`      // 第三方退款单号
	PaymentMethod PaymentMethod `json:"payment_method"` // 支付方式
	Status        string        `json:"status"`         // 退款状态
	Amount        int64         `json:"amount"`         // 退款金额(分)
	SuccessTime   *time.Time    `json:"success_time"`   // 退款成功时间
}

// NotifyData 支付回调数据
type NotifyData struct {
	PaymentMethod PaymentMethod          `json:"payment_method"` // 支付方式
	OutTradeNo    string                 `json:"out_trade_no"`   // 商户订单号
	TransactionID string                 `json:"transaction_id"` // 第三方交易号
	Status        PaymentStatus          `json:"status"`         // 支付状态
	Amount        int64                  `json:"amount"`         // 订单金额(分)
	PaidAmount    int64                  `json:"paid_amount"`    // 实际支付金额(分)
	PaidTime      *time.Time             `json:"paid_time"`      // 支付时间
	AttachData    string                 `json:"attach"`         // 附加数据
	RawData       map[string]interface{} `json:"raw_data"`       // 原始回调数据
}
