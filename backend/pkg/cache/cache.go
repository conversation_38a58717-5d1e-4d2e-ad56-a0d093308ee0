package cache

import "time"

// Cache 缓存接口
type Cache interface {
	Get(key string) (string, error)
	Set(key string, value string, ttl time.Duration) error
	Delete(key string) error
	Exists(key string) bool
}

// MemoryCache 内存缓存实现
type MemoryCache struct {
	data map[string]string
}

func NewMemoryCache() Cache {
	return &MemoryCache{
		data: make(map[string]string),
	}
}

func (m *MemoryCache) Get(key string) (string, error) {
	value, ok := m.data[key]
	if !ok {
		return "", nil
	}
	return value, nil
}

func (m *MemoryCache) Set(key string, value string, ttl time.Duration) error {
	m.data[key] = value
	// TODO: 实现TTL过期机制
	return nil
}

func (m *MemoryCache) Delete(key string) error {
	delete(m.data, key)
	return nil
}

func (m *MemoryCache) Exists(key string) bool {
	_, ok := m.data[key]
	return ok
}
