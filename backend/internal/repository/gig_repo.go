package repository

import (
	"context"
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/internal/types"

	"gorm.io/gorm"
)

type GigRepository interface {
	Create(ctx context.Context, gig *model.Gig) error
	GetByID(ctx context.Context, id uint) (*model.Gig, error)
	Update(ctx context.Context, gig *model.Gig) error
	Delete(ctx context.Context, id uint) error
	GetList(ctx context.Context, req *types.GigListReq) ([]*model.Gig, int64, error)
	GetByUserID(ctx context.Context, userID uint, status string) ([]model.Gig, error)
	IncrementApplyCount(ctx context.Context, id uint) error
	GetExpiredGigs(ctx context.Context) ([]model.Gig, error)

	// Calendar related methods
	GetByUserIDAndDateRange(ctx context.Context, userID uint, startTime, endTime time.Time) ([]*model.Gig, error)
	GetApplicationsByUserIDAndDateRange(ctx context.Context, userID uint, startTime, endTime time.Time) ([]*model.GigApplication, error)

	CreateApplication(ctx context.Context, app *model.GigApplication) error
	GetApplicationByID(ctx context.Context, id uint) (*model.GigApplication, error)
	UpdateApplication(ctx context.Context, app *model.GigApplication) error
	CheckUserApplied(ctx context.Context, gigID, userID uint) bool
	GetUserApplications(ctx context.Context, userID uint, page, pageSize int) ([]*model.GigApplication, int64, error)
	GetGigApplications(ctx context.Context, gigID uint, page, pageSize int) ([]*model.GigApplication, int64, error)

	// DB 返回底层的 gorm.DB 实例
	DB() *gorm.DB
}

type gigRepository struct {
	db *gorm.DB
}

func NewGigRepository(db *gorm.DB) GigRepository {
	return &gigRepository{db: db}
}

// DB 返回底层的 gorm.DB 实例
func (r *gigRepository) DB() *gorm.DB {
	return r.db
}

func (r *gigRepository) Create(ctx context.Context, gig *model.Gig) error {
	return r.db.WithContext(ctx).Create(gig).Error
}

func (r *gigRepository) GetByID(ctx context.Context, id uint) (*model.Gig, error) {
	var gig model.Gig
	err := r.db.WithContext(ctx).First(&gig, id).Error
	return &gig, err
}

func (r *gigRepository) Update(ctx context.Context, gig *model.Gig) error {
	return r.db.WithContext(ctx).Save(gig).Error
}

func (r *gigRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Gig{}, id).Error
}

func (r *gigRepository) GetList(ctx context.Context, req *types.GigListReq) ([]*model.Gig, int64, error) {
	// Placeholder implementation
	var gigs []*model.Gig
	var total int64
	db := r.db.WithContext(ctx).Model(&model.Gig{})

	// TODO: Add filtering logic based on req

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(req.Page, req.PageSize)).Find(&gigs).Error
	return gigs, total, err
}

func (r *gigRepository) GetByUserID(ctx context.Context, userID uint, status string) ([]model.Gig, error) {
	var gigs []model.Gig
	query := r.db.WithContext(ctx).Where("user_id = ?", userID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Order("created_at DESC").Find(&gigs).Error
	return gigs, err
}

func (r *gigRepository) IncrementApplyCount(ctx context.Context, id uint) error {
	// This might be handled by `current_people_count` now.
	// Re-evaluating if this is still needed. For now, a placeholder.
	return nil
}

func (r *gigRepository) GetExpiredGigs(ctx context.Context) ([]model.Gig, error) {
	// Placeholder
	var gigs []model.Gig
	return gigs, nil
}

func (r *gigRepository) CreateApplication(ctx context.Context, app *model.GigApplication) error {
	return r.db.WithContext(ctx).Create(app).Error
}

func (r *gigRepository) GetApplicationByID(ctx context.Context, id uint) (*model.GigApplication, error) {
	var app model.GigApplication
	err := r.db.WithContext(ctx).Preload("GigInfo").Preload("ApplicantInfo").First(&app, id).Error
	return &app, err
}

func (r *gigRepository) UpdateApplication(ctx context.Context, app *model.GigApplication) error {
	return r.db.WithContext(ctx).Save(app).Error
}

func (r *gigRepository) CheckUserApplied(ctx context.Context, gigID, userID uint) bool {
	var count int64
	r.db.WithContext(ctx).Model(&model.GigApplication{}).Where("gig_id = ? AND user_id = ?", gigID, userID).Count(&count)
	return count > 0
}

func (r *gigRepository) GetUserApplications(ctx context.Context, userID uint, page, pageSize int) ([]*model.GigApplication, int64, error) {
	var apps []*model.GigApplication
	var total int64
	db := r.db.WithContext(ctx).Model(&model.GigApplication{}).Where("user_id = ?", userID)

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).Find(&apps).Error
	return apps, total, err
}

func (r *gigRepository) GetGigApplications(ctx context.Context, gigID uint, page, pageSize int) ([]*model.GigApplication, int64, error) {
	var apps []*model.GigApplication
	var total int64
	db := r.db.WithContext(ctx).Model(&model.GigApplication{}).Where("gig_id = ?", gigID)

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).Find(&apps).Error
	return apps, total, err
}

// GetByUserIDAndDateRange 根据用户ID和日期范围获取零工列表
func (r *gigRepository) GetByUserIDAndDateRange(ctx context.Context, userID uint, startTime, endTime time.Time) ([]*model.Gig, error) {
	var gigs []*model.Gig
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND start_time >= ? AND start_time <= ?", userID, startTime, endTime).
		Order("start_time ASC").
		Find(&gigs).Error
	return gigs, err
}

// GetApplicationsByUserIDAndDateRange 根据用户ID和日期范围获取申请列表
func (r *gigRepository) GetApplicationsByUserIDAndDateRange(ctx context.Context, userID uint, startTime, endTime time.Time) ([]*model.GigApplication, error) {
	var apps []*model.GigApplication
	err := r.db.WithContext(ctx).
		Joins("JOIN gigs ON gig_applications.gig_id = gigs.id").
		Where("gig_applications.user_id = ? AND gigs.start_time >= ? AND gigs.start_time <= ?", userID, startTime, endTime).
		Order("gig_applications.created_at ASC").
		Find(&apps).Error
	return apps, err
}
