package repository

import (
	"context"

	"bdb-backend/internal/model"

	"gorm.io/gorm"
)

// ConversationRepository 会话Repository接口
type ConversationRepository interface {
	// 会话管理
	Create(ctx context.Context, conversation *model.Conversation) error
	GetByID(ctx context.Context, id uint) (*model.Conversation, error)
	GetByUsers(ctx context.Context, userID1, userID2 uint, conversationType model.ConversationType) (*model.Conversation, error)
	Update(ctx context.Context, id uint, updates map[string]interface{}) error
	Delete(ctx context.Context, id uint) error

	// 查询方法
	ListByUser(ctx context.Context, userID uint, page, pageSize int) ([]*model.Conversation, int64, error)
	GetSystemConversation(ctx context.Context, userID uint) (*model.Conversation, error)

	// 批量操作
	BatchCreate(ctx context.Context, conversations []*model.Conversation) error
}

// ConversationFilter 会话查询过滤器
type ConversationFilter struct {
	Type   model.ConversationType
	Offset int
	Limit  int
}

// conversationRepository 会话Repository实现
type conversationRepository struct {
	db *gorm.DB
}

// NewConversationRepository 创建会话Repository
func NewConversationRepository(db *gorm.DB) ConversationRepository {
	return &conversationRepository{db: db}
}

// Create 创建会话
func (r *conversationRepository) Create(ctx context.Context, conversation *model.Conversation) error {
	return r.db.WithContext(ctx).Create(conversation).Error
}

// GetByID 根据ID获取会话
func (r *conversationRepository) GetByID(ctx context.Context, id uint) (*model.Conversation, error) {
	var conversation model.Conversation
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&conversation).Error
	if err != nil {
		return nil, err
	}
	return &conversation, nil
}

// GetByUsers 根据用户获取会话
func (r *conversationRepository) GetByUsers(ctx context.Context, userID1, userID2 uint, conversationType model.ConversationType) (*model.Conversation, error) {
	// 确保 userID1 < userID2 (除非是系统通知)
	if conversationType == model.ConversationTypeSingle && userID1 > userID2 {
		userID1, userID2 = userID2, userID1
	}

	var conversation model.Conversation
	err := r.db.WithContext(ctx).Where(
		"user_id1 = ? AND user_id2 = ? AND type = ?",
		userID1, userID2, conversationType,
	).First(&conversation).Error

	if err != nil {
		return nil, err
	}
	return &conversation, nil
}

// Update 更新会话
func (r *conversationRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&model.Conversation{}).Where("id = ?", id).Updates(updates).Error
}

// Delete 删除会话
func (r *conversationRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Where("id = ?", id).Delete(&model.Conversation{}).Error
}

// ListByUser 获取用户的会话列表
func (r *conversationRepository) ListByUser(ctx context.Context, userID uint, page, pageSize int) ([]*model.Conversation, int64, error) {
	query := r.db.WithContext(ctx).Model(&model.Conversation{}).Where(
		"(user_id1 = ? OR user_id2 = ?)", userID, userID,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	var conversations []*model.Conversation
	err := query.Order("created_at DESC").
		Scopes(model.Paginate(page, pageSize)).
		Find(&conversations).Error

	return conversations, total, err
}

// GetSystemConversation 获取用户的系统通知会话
func (r *conversationRepository) GetSystemConversation(ctx context.Context, userID uint) (*model.Conversation, error) {
	var conversation model.Conversation
	err := r.db.WithContext(ctx).Where(
		"user_id1 = 0 AND user_id2 = ? AND type = ?",
		userID, model.ConversationTypeSystem,
	).First(&conversation).Error

	if err != nil {
		return nil, err
	}
	return &conversation, nil
}

// BatchCreate 批量创建会话
func (r *conversationRepository) BatchCreate(ctx context.Context, conversations []*model.Conversation) error {
	if len(conversations) == 0 {
		return nil
	}
	return r.db.WithContext(ctx).CreateInBatches(conversations, 100).Error
}
