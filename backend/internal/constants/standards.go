package constants

// =====================================================
// 数据标准化常量定义
// =====================================================

// 性别标准
const (
	GenderUnlimited = 0 // 不限
	GenderMale      = 1 // 男
	GenderFemale    = 2 // 女
)

// 年龄范围标准 (简化版)
const (
	AgeUnlimited = 0 // 不限
	Age18To30    = 1 // 18-30岁
	Age31To40    = 2 // 31-40岁
	Age41To50    = 3 // 41-50岁
	Age50Plus    = 4 // 50岁以上
)

// 学历标准 (移除博士)
const (
	EducationUnlimited  = 0 // 不限
	EducationJuniorHigh = 1 // 初中及以下
	EducationHighSchool = 2 // 高中/中专
	EducationAssociate  = 3 // 大专
	EducationBachelor   = 4 // 本科
	EducationMaster     = 5 // 硕士
)

// 工作经验标准
const (
	ExperienceUnlimited     = 0 // 不限
	ExperienceFreshGraduate = 1 // 应届毕业生
	ExperienceUnder1Year    = 2 // 1年以下
	Experience1To3Years     = 3 // 1-3年
	Experience3To5Years     = 4 // 3-5年
	Experience5To10Years    = 5 // 5-10年
	ExperienceOver10Years   = 6 // 10年以上
)

// 零工薪资单位标准
const (
	SalaryUnitUnlimited = 0 // 不限
	SalaryUnitHour      = 1 // 按小时
	SalaryUnitDay       = 2 // 按天
	SalaryUnitPiece     = 3 // 按件
	SalaryUnitTotal     = 4 // 一口价
)

// 结算方式标准
const (
	SettlementTypeUnlimited = 0 // 不限
	SettlementTypeDaily     = 1 // 日结
	SettlementTypeWeekly    = 2 // 周结
	SettlementTypeMonthly   = 3 // 月结
	SettlementTypeFinished  = 4 // 完工结
)

// 零工薪资标准 (简化版 - 按小时，单位：分)
const (
	GigSalaryUnlimited = 0 // 不限
	GigSalary20To40    = 1 // 20-40元/小时 (2000-4000分)
	GigSalary40To70    = 2 // 40-70元/小时 (4000-7000分)
	GigSalary70To100   = 3 // 70-100元/小时 (7000-10000分)
	GigSalaryOver100   = 4 // 100元以上/小时 (10000分以上)
)

// 正职薪资标准 (按月，单位：分)
const (
	JobSalaryUnlimited = 0 // 不限
	JobSalary3KTo6K    = 1 // 3K-6K (300000-600000分)
	JobSalary6KTo10K   = 2 // 6K-10K (600000-1000000分)
	JobSalary10KTo15K  = 3 // 10K-15K (1000000-1500000分)
	JobSalary15KTo25K  = 4 // 15K-25K (1500000-2500000分)
	JobSalaryOver25K   = 5 // 25K以上 (2500000分以上)
)

// 公司规模标准 (使用数值编码)
const (
	CompanySizeUnlimited  = 0 // 不限
	CompanySizeStartup    = 1 // 1-20人
	CompanySizeSmall      = 2 // 21-100人
	CompanySizeMedium     = 3 // 101-500人
	CompanySizeLarge      = 4 // 501-1000人
	CompanySizeEnterprise = 5 // 1000人以上
)

// 福利待遇标准 (JSON数组，适合中低端岗位)
const (
	// 基础保障类
	WelfareSocialInsurance = 1 // 五险一金
	WelfareInsurance       = 2 // 五险
	WelfareAccommodation   = 3 // 包食宿
	WelfareFood            = 4 // 包吃
	WelfareHousing         = 5 // 包住
	WelfareMealAllowance   = 6 // 餐补

	// 薪酬奖励类
	WelfareAnnualBonus      = 7  // 年终奖
	WelfarePerformanceBonus = 8  // 绩效奖金
	WelfareFullAttendance   = 9  // 全勤奖
	WelfareOvertimePay      = 10 // 加班费

	// 时间福利类
	WelfarePaidLeave     = 11 // 带薪年假
	WelfareFlexibleWork  = 12 // 弹性工作
	WelfareWeekendOff    = 13 // 双休
	WelfareLegalHolidays = 14 // 法定节假日

	// 交通住宿类
	WelfareTransportAllowance = 15 // 交通补贴
	WelfareDormitory          = 16 // 宿舍
	WelfareShuttleBus         = 17 // 班车接送

	// 发展培训类
	WelfareTraining      = 18 // 培训机会
	WelfarePromotionPath = 19 // 晋升通道
	WelfareSkillTraining = 20 // 技能培训

	// 生活关怀类
	WelfareHolidayBenefits  = 21 // 节日福利
	WelfareBirthdayBenefit  = 22 // 生日福利
	WelfareTeamBuilding     = 23 // 团建活动
	WelfareEmployeeDiscount = 24 // 员工优惠

	// 健康医疗类
	WelfareMedicalExam         = 25 // 体检
	WelfareCommercialInsurance = 26 // 商业保险

)

// =====================================================
// 标准选项数据结构
// =====================================================

// StandardOption 标准选项结构
type StandardOption struct {
	Code  int    `json:"code"`
	Label string `json:"label"`
	Value string `json:"value"`
	Min   *int   `json:"min,omitempty"` // 最小值（用于范围类型）
	Max   *int   `json:"max,omitempty"` // 最大值（用于范围类型）
}

// SalaryRange 薪资范围结构
type SalaryRange struct {
	Code int `json:"code"`
	Min  int `json:"min"` // 最小值（分）
	Max  int `json:"max"` // 最大值（分）
}

// AgeRange 年龄范围结构
type AgeRange struct {
	Code int `json:"code"`
	Min  int `json:"min"` // 最小年龄
	Max  int `json:"max"` // 最大年龄
}

// CompanySizeRange 公司规模范围结构
type CompanySizeRange struct {
	Code int `json:"code"`
	Min  int `json:"min"` // 最小人数
	Max  int `json:"max"` // 最大人数
}

// =====================================================
// 预定义选项数据
// =====================================================

// GenderOptions 性别选项
var GenderOptions = []StandardOption{
	{Code: GenderUnlimited, Label: "不限", Value: "不限"},
	{Code: GenderMale, Label: "男", Value: "男"},
	{Code: GenderFemale, Label: "女", Value: "女"},
}

// AgeRangeOptions 年龄范围选项
var AgeRangeOptions = []StandardOption{
	{Code: AgeUnlimited, Label: "不限", Value: "不限"},
	{Code: Age18To30, Label: "18-30岁", Value: "18-30岁"},
	{Code: Age31To40, Label: "31-40岁", Value: "31-40岁"},
	{Code: Age41To50, Label: "41-50岁", Value: "41-50岁"},
	{Code: Age50Plus, Label: "50岁以上", Value: "50岁以上"},
}

// EducationOptions 学历选项
var EducationOptions = []StandardOption{
	{Code: EducationUnlimited, Label: "不限", Value: "不限"},
	{Code: EducationJuniorHigh, Label: "初中及以下", Value: "初中及以下"},
	{Code: EducationHighSchool, Label: "高中/中专", Value: "高中/中专"},
	{Code: EducationAssociate, Label: "大专", Value: "大专"},
	{Code: EducationBachelor, Label: "本科", Value: "本科"},
	{Code: EducationMaster, Label: "硕士", Value: "硕士"},
}

// ExperienceOptions 工作经验选项
var ExperienceOptions = []StandardOption{
	{Code: ExperienceUnlimited, Label: "不限", Value: "不限"},
	{Code: ExperienceFreshGraduate, Label: "应届毕业生", Value: "应届毕业生"},
	{Code: ExperienceUnder1Year, Label: "1年以下", Value: "1年以下"},
	{Code: Experience1To3Years, Label: "1-3年", Value: "1-3年"},
	{Code: Experience3To5Years, Label: "3-5年", Value: "3-5年"},
	{Code: Experience5To10Years, Label: "5-10年", Value: "5-10年"},
	{Code: ExperienceOver10Years, Label: "10年以上", Value: "10年以上"},
}

// SalaryUnitOptions 零工薪资单位选项
var SalaryUnitOptions = []StandardOption{
	{Code: SalaryUnitUnlimited, Label: "不限", Value: "unlimited"},
	{Code: SalaryUnitHour, Label: "元/小时", Value: "hour"},
	{Code: SalaryUnitDay, Label: "元/天", Value: "day"},
	{Code: SalaryUnitPiece, Label: "元/件", Value: "piece"},
	{Code: SalaryUnitTotal, Label: "元/总价", Value: "total"},
}

// SettlementTypeOptions 结算方式选项
var SettlementTypeOptions = []StandardOption{
	{Code: SettlementTypeUnlimited, Label: "不限", Value: "unlimited"},
	{Code: SettlementTypeDaily, Label: "日结", Value: "daily"},
	{Code: SettlementTypeWeekly, Label: "周结", Value: "weekly"},
	{Code: SettlementTypeMonthly, Label: "月结", Value: "monthly"},
	{Code: SettlementTypeFinished, Label: "完工结", Value: "finished"},
}

// CompanySizeOptions 公司规模选项
var CompanySizeOptions = []StandardOption{
	{Code: CompanySizeUnlimited, Label: "不限", Value: "不限"},
	{Code: CompanySizeStartup, Label: "1-20人", Value: "1-20人", Min: intPtr(1), Max: intPtr(20)},
	{Code: CompanySizeSmall, Label: "21-100人", Value: "21-100人", Min: intPtr(21), Max: intPtr(100)},
	{Code: CompanySizeMedium, Label: "101-500人", Value: "101-500人", Min: intPtr(101), Max: intPtr(500)},
	{Code: CompanySizeLarge, Label: "501-1000人", Value: "501-1000人", Min: intPtr(501), Max: intPtr(1000)},
	{Code: CompanySizeEnterprise, Label: "1000人以上", Value: "1000人以上", Min: intPtr(1001), Max: intPtr(999999)},
}

// WelfareOptions 福利待遇选项（多选，JSON数组）
var WelfareOptions = []StandardOption{
	// 基础保障类
	{Code: WelfareSocialInsurance, Label: "五险一金", Value: "五险一金"},
	{Code: WelfareInsurance, Label: "五险", Value: "五险"},
	{Code: WelfareAccommodation, Label: "包食宿", Value: "包食宿"},
	{Code: WelfareFood, Label: "包吃", Value: "包吃"},
	{Code: WelfareHousing, Label: "包住", Value: "包住"},
	{Code: WelfareMealAllowance, Label: "餐补", Value: "餐补"},

	// 薪酬奖励类
	{Code: WelfareAnnualBonus, Label: "年终奖", Value: "年终奖"},
	{Code: WelfarePerformanceBonus, Label: "绩效奖金", Value: "绩效奖金"},
	{Code: WelfareFullAttendance, Label: "全勤奖", Value: "全勤奖"},
	{Code: WelfareOvertimePay, Label: "加班费", Value: "加班费"},

	// 时间福利类
	{Code: WelfarePaidLeave, Label: "带薪年假", Value: "带薪年假"},
	{Code: WelfareFlexibleWork, Label: "弹性工作", Value: "弹性工作"},
	{Code: WelfareWeekendOff, Label: "双休", Value: "双休"},
	{Code: WelfareLegalHolidays, Label: "法定节假日", Value: "法定节假日"},

	// 交通住宿类
	{Code: WelfareTransportAllowance, Label: "交通补贴", Value: "交通补贴"},
	{Code: WelfareDormitory, Label: "宿舍", Value: "宿舍"},
	{Code: WelfareShuttleBus, Label: "班车接送", Value: "班车接送"},

	// 发展培训类
	{Code: WelfareTraining, Label: "培训机会", Value: "培训机会"},
	{Code: WelfarePromotionPath, Label: "晋升通道", Value: "晋升通道"},
	{Code: WelfareSkillTraining, Label: "技能培训", Value: "技能培训"},

	// 生活关怀类
	{Code: WelfareHolidayBenefits, Label: "节日福利", Value: "节日福利"},
	{Code: WelfareBirthdayBenefit, Label: "生日福利", Value: "生日福利"},
	{Code: WelfareTeamBuilding, Label: "团建活动", Value: "团建活动"},
	{Code: WelfareEmployeeDiscount, Label: "员工优惠", Value: "员工优惠"},

	// 健康医疗类
	{Code: WelfareMedicalExam, Label: "体检", Value: "体检"},
	{Code: WelfareCommercialInsurance, Label: "商业保险", Value: "商业保险"},
}

// =====================================================
// 薪资和范围映射
// =====================================================

// GigSalaryRanges 零工薪资范围映射 (单位：分/小时)
var GigSalaryRanges = map[int]SalaryRange{
	GigSalaryUnlimited: {Code: GigSalaryUnlimited, Min: 0, Max: 0},
	GigSalary20To40:    {Code: GigSalary20To40, Min: 2000, Max: 4000},
	GigSalary40To70:    {Code: GigSalary40To70, Min: 4000, Max: 7000},
	GigSalary70To100:   {Code: GigSalary70To100, Min: 7000, Max: 10000},
	GigSalaryOver100:   {Code: GigSalaryOver100, Min: 10000, Max: 999999},
}

// JobSalaryRanges 正职薪资范围映射 (单位：分/月)
var JobSalaryRanges = map[int]SalaryRange{
	JobSalaryUnlimited: {Code: JobSalaryUnlimited, Min: 0, Max: 0},
	JobSalary3KTo6K:    {Code: JobSalary3KTo6K, Min: 300000, Max: 600000},
	JobSalary6KTo10K:   {Code: JobSalary6KTo10K, Min: 600000, Max: 1000000},
	JobSalary10KTo15K:  {Code: JobSalary10KTo15K, Min: 1000000, Max: 1500000},
	JobSalary15KTo25K:  {Code: JobSalary15KTo25K, Min: 1500000, Max: 2500000},
	JobSalaryOver25K:   {Code: JobSalaryOver25K, Min: 2500000, Max: 99999999},
}

// AgeRanges 年龄范围映射
var AgeRanges = map[int]AgeRange{
	AgeUnlimited: {Code: AgeUnlimited, Min: 0, Max: 0},
	Age18To30:    {Code: Age18To30, Min: 18, Max: 30},
	Age31To40:    {Code: Age31To40, Min: 31, Max: 40},
	Age41To50:    {Code: Age41To50, Min: 41, Max: 50},
	Age50Plus:    {Code: Age50Plus, Min: 51, Max: 100},
}

// CompanySizeRanges 公司规模范围映射
var CompanySizeRanges = map[int]CompanySizeRange{
	CompanySizeUnlimited:  {Code: CompanySizeUnlimited, Min: 0, Max: 0},
	CompanySizeStartup:    {Code: CompanySizeStartup, Min: 1, Max: 20},
	CompanySizeSmall:      {Code: CompanySizeSmall, Min: 21, Max: 100},
	CompanySizeMedium:     {Code: CompanySizeMedium, Min: 101, Max: 500},
	CompanySizeLarge:      {Code: CompanySizeLarge, Min: 501, Max: 1000},
	CompanySizeEnterprise: {Code: CompanySizeEnterprise, Min: 1001, Max: 999999},
}

// =====================================================
// 工具函数
// =====================================================

// intPtr 创建int指针的辅助函数
func intPtr(i int) *int {
	return &i
}

// GetGigSalaryRange 获取零工薪资范围
func GetGigSalaryRange(code int) (SalaryRange, bool) {
	r, ok := GigSalaryRanges[code]
	return r, ok
}

// GetJobSalaryRange 获取正职薪资范围
func GetJobSalaryRange(code int) (SalaryRange, bool) {
	r, ok := JobSalaryRanges[code]
	return r, ok
}

// GetAgeRange 获取年龄范围
func GetAgeRange(code int) (AgeRange, bool) {
	r, ok := AgeRanges[code]
	return r, ok
}

// GetCompanySizeRange 获取公司规模范围
func GetCompanySizeRange(code int) (CompanySizeRange, bool) {
	r, ok := CompanySizeRanges[code]
	return r, ok
}

// GetLabelByCode 根据选项列表和代码获取标签
func GetLabelByCode(options []StandardOption, code int16) string {
	for _, option := range options {
		if option.Code == int(code) {
			return option.Label
		}
	}
	return ""
}

// GetWelfareLabels 根据编码数组获取福利标签
func GetWelfareLabels(codes []int) []string {
	labels := make([]string, 0, len(codes))
	for _, code := range codes {
		for _, option := range WelfareOptions {
			if option.Code == code {
				labels = append(labels, option.Label)
				break
			}
		}
	}
	return labels
}

// GetWelfareByCode 根据编码获取福利选项
func GetWelfareByCode(code int) (StandardOption, bool) {
	for _, option := range WelfareOptions {
		if option.Code == code {
			return option, true
		}
	}
	return StandardOption{}, false
}

// ContainsWelfare 检查福利编码数组是否包含指定福利
func ContainsWelfare(codes []int, targetCode int) bool {
	for _, code := range codes {
		if code == targetCode {
			return true
		}
	}
	return false
}
