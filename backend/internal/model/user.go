package model

import (
	"time"

	"gorm.io/datatypes"
)

// User 用户模型
type User struct {
	BaseModel
	UID                    string         `gorm:"type:varchar(64);not null;uniqueIndex:uk_users_uid,where:is_del=0" json:"uid"`
	OpenID                 string         `gorm:"column:openid;type:varchar(128);not null;uniqueIndex:uk_users_openid,where:is_del=0" json:"openid"`
	UnionID                *string        `gorm:"column:unionid;type:varchar(128)" json:"unionid"`
	Phone                  string         `gorm:"column:phone;type:varchar(20);not null;uniqueIndex:uk_users_phone,where:is_del=0" json:"phone"`
	Nickname               string         `gorm:"column:nickname;type:varchar(50)" json:"nickname"`
	Avatar                 string         `gorm:"column:avatar;type:varchar(255)" json:"avatar"`
	Gender                 int            `gorm:"default:0" json:"gender"` // 0:未知 1:男 2:女
	Status                 int            `gorm:"default:1;index" json:"status"`
	Birthday               *time.Time     `gorm:"type:date" json:"birthday"`
	IsVerified             bool           `gorm:"default:false;index" json:"is_verified"`
	PersonalVerificationID uint           `gorm:"default:0" json:"personal_verification_id"` // 个人认证记录ID
	EnterpriseID           uint           `gorm:"default:0;index" json:"enterprise_id"`      // 关联企业ID：0-未认证企业
	Points                 int            `gorm:"default:0" json:"points"`
	PhoneChangedAt         *time.Time     `gorm:"type:timestamp(0)" json:"phone_changed_at"`
	Source                 string         `gorm:"type:varchar(20);not null;default:'mp-weixin'" json:"source"`
	Settings               datatypes.JSON `gorm:"type:jsonb;default:'{}'" json:"settings"`
	LastLoginAt            *time.Time     `gorm:"type:timestamp(0)" json:"last_login_at"`
}

func (u User) TableName() string {
	return "users"
}

// UserSettings 用户设置 暂时没有用到
type UserSettings struct {
	UserID              uint      `gorm:"primarykey" json:"user_id"`
	AllowMessageFrom    int       `gorm:"default:1" json:"allow_message_from"` // 1:所有人 2:关注的人 3:不允许
	AllowLocationShare  bool      `gorm:"default:true" json:"allow_location_share"`
	AllowJobRecommend   bool      `gorm:"default:true" json:"allow_job_recommend"`
	AllowHouseRecommend bool      `gorm:"default:true" json:"allow_house_recommend"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}
