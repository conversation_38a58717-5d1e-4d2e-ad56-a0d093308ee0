package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"

	"gorm.io/gorm"
)

// MessageService 消息服务接口
type MessageService interface {
	SendMessage(ctx context.Context, req *types.SendMessageRequest) (*types.SendMessageResponse, error)
	GetMessageList(ctx context.Context, req *types.GetMessageListRequest) (*types.GetMessageListResponse, error)
	MarkMessagesAsRead(ctx context.Context, req *types.MarkMessagesAsReadRequest) (*types.MarkMessagesAsReadResponse, error)
	RevokeMessage(ctx context.Context, req *types.RevokeMessageRequest) (*types.RevokeMessageResponse, error)
	GetMessageStatus(ctx context.Context, messageID uint) (string, error)
}

// messageService 消息服务实现
type messageService struct {
	messageRepo          repository.MessageRepository
	conversationRepo     repository.ConversationRepository
	userConversationRepo repository.UserConversationRepository
	conversationService  ConversationService
	db                   *gorm.DB
}

// NewMessageService 创建消息服务
func NewMessageService(
	messageRepo repository.MessageRepository,
	conversationRepo repository.ConversationRepository,
	userConversationRepo repository.UserConversationRepository,
	conversationService ConversationService,
	db *gorm.DB,
) MessageService {
	return &messageService{
		messageRepo:          messageRepo,
		conversationRepo:     conversationRepo,
		userConversationRepo: userConversationRepo,
		conversationService:  conversationService,
		db:                   db,
	}
}

// SendMessage 发送消息
func (s *messageService) SendMessage(ctx context.Context, req *types.SendMessageRequest) (*types.SendMessageResponse, error) {
	// 验证参数
	if req.SenderID == 0 || req.ReceiverID == 0 {
		return nil, errors.New("发送者和接收者ID不能为空")
	}
	if req.SenderID == req.ReceiverID {
		return nil, errors.New("不能给自己发送消息")
	}
	if req.Content == "" {
		return nil, errors.New("消息内容不能为空")
	}

	// 检查消息是否已存在（防重复）
	if req.MsgID != "" {
		existingMsg, err := s.messageRepo.GetByMsgID(ctx, req.MsgID)
		if err == nil && existingMsg != nil {
			// 消息已存在，返回现有消息（幂等性）
			return &types.SendMessageResponse{
				MessageID: existingMsg.ID,
				MsgID:     existingMsg.MsgID,
				Status:    string(existingMsg.Status),
				SentAt:    existingMsg.CreatedAt,
			}, nil
		}
	}

	// 获取或创建会话
	conversation, err := s.conversationService.GetOrCreateSingleConversation(ctx, req.SenderID, req.ReceiverID)
	if err != nil {
		return nil, fmt.Errorf("获取或创建会话失败: %w", err)
	}

	// 生成消息ID（如果客户端没有提供）
	msgID := req.MsgID
	if msgID == "" {
		msgID = fmt.Sprintf("msg_%d_%d", time.Now().UnixNano(), req.SenderID)
	}

	// 创建消息
	message := &model.Message{
		MsgID:          msgID,
		ConversationID: conversation.ID,
		SenderID:       req.SenderID,
		Content:        req.Content,
		MessageType:    model.ChatMessageType(req.MessageType),
		Status:         model.ChatMessageStatusSent,
		Extra:          req.Extra,
	}

	// 开启事务
	tx := s.db.Begin()
	defer tx.Rollback()

	// 保存消息
	if err := s.messageRepo.Create(ctx, message); err != nil {
		return nil, fmt.Errorf("保存消息失败: %w", err)
	}

	// 更新发送方会话状态
	now := time.Now()
	senderUpdates := map[string]interface{}{
		"last_active_time": now,
		"is_visible":       true,
	}
	if err := s.userConversationRepo.Update(ctx, req.SenderID, conversation.ID, senderUpdates); err != nil {
		return nil, fmt.Errorf("更新发送方会话状态失败: %w", err)
	}

	// 更新接收方会话状态（恢复可见性）
	receiverUpdates := map[string]interface{}{
		"last_active_time": now,
		"is_visible":       true,
		"is_deleted":       false,
	}
	if err := s.userConversationRepo.Update(ctx, req.ReceiverID, conversation.ID, receiverUpdates); err != nil {
		return nil, fmt.Errorf("更新接收方会话状态失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// TODO: 异步处理推送通知
	// go s.pushMessage(ctx, message)

	return &types.SendMessageResponse{
		MessageID: message.ID,
		MsgID:     message.MsgID,
		Status:    string(message.Status),
		SentAt:    message.CreatedAt,
	}, nil
}

// GetMessageList 获取消息列表
func (s *messageService) GetMessageList(ctx context.Context, req *types.GetMessageListRequest) (*types.GetMessageListResponse, error) {
	// 验证用户是否有访问该会话的权限
	userConv, err := s.userConversationRepo.GetByUserAndConversation(ctx, req.UserID, req.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("获取用户会话状态失败: %w", err)
	}
	if !userConv.IsVisible {
		return nil, errors.New("无权访问该会话")
	}

	// 获取消息列表
	messages, err := s.messageRepo.ListByConversation(ctx, req.ConversationID, req.Page, req.PageSize)
	if err != nil {
		return nil, fmt.Errorf("获取消息列表失败: %w", err)
	}

	// 构建响应
	messageInfos := make([]*types.MessageInfo, 0, len(messages))
	for _, msg := range messages {
		messageInfo := &types.MessageInfo{
			ID:          msg.ID,
			MsgID:       msg.MsgID,
			SenderID:    msg.SenderID,
			Content:     msg.Content,
			MessageType: types.ChatMessageType(msg.MessageType),
			Status:      types.ChatMessageStatus(msg.Status),
			IsRevoked:   msg.IsRevoked,
			CreatedAt:   msg.CreatedAt,
		}
		if msg.Extra != nil {
			messageInfo.Extra = msg.Extra
		}
		messageInfos = append(messageInfos, messageInfo)
	}

	// 计算总数（需要单独查询，因为ListByConversation不返回总数）
	total, _ := s.messageRepo.CountMessagesByConversation(ctx, req.ConversationID)

	return &types.GetMessageListResponse{
		Messages: messageInfos,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, nil
}

// MarkMessagesAsRead 标记消息为已读
func (s *messageService) MarkMessagesAsRead(ctx context.Context, req *types.MarkMessagesAsReadRequest) (*types.MarkMessagesAsReadResponse, error) {
	// 获取用户会话状态
	userConv, err := s.userConversationRepo.GetByUserAndConversation(ctx, req.UserID, req.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("获取用户会话状态失败: %w", err)
	}

	// 获取会话的最后一条消息
	lastMessage, err := s.messageRepo.GetLastMessage(ctx, req.ConversationID)
	if err != nil {
		return nil, fmt.Errorf("获取最后消息失败: %w", err)
	}

	// 如果最后一条消息已经被标记为已读，则无需操作
	if userConv.LastReadMessageID >= lastMessage.ID {
		return &types.MarkMessagesAsReadResponse{
			Success:   true,
			ReadCount: 0,
		}, nil
	}

	// 计算未读消息数量
	unreadCount, err := s.messageRepo.CountUnreadMessages(ctx, req.UserID, req.ConversationID, userConv.LastReadMessageID)
	if err != nil {
		return nil, fmt.Errorf("计算未读消息数量失败: %w", err)
	}

	if unreadCount == 0 {
		return &types.MarkMessagesAsReadResponse{
			Success:   true,
			ReadCount: 0,
		}, nil
	}

	// 开启事务
	tx := s.db.Begin()
	defer tx.Rollback()

	// 标记用户会话的已读状态
	if err := s.userConversationRepo.MarkAsRead(ctx, req.UserID, req.ConversationID, lastMessage.ID); err != nil {
		return nil, fmt.Errorf("更新用户会话已读状态失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// TODO: 异步处理已读回执推送
	// go s.pushReadReceipt(ctx, req.UserID, req.ConversationID, lastMessage.ID)

	return &types.MarkMessagesAsReadResponse{
		Success:   true,
		ReadCount: int(unreadCount),
	}, nil
}

// RevokeMessage 撤回消息
func (s *messageService) RevokeMessage(ctx context.Context, req *types.RevokeMessageRequest) (*types.RevokeMessageResponse, error) {
	// 获取消息
	message, err := s.messageRepo.GetByID(ctx, req.MessageID)
	if err != nil {
		return nil, fmt.Errorf("获取消息失败: %w", err)
	}

	// 验证权限（只能撤回自己的消息）
	if message.SenderID != req.UserID {
		return nil, errors.New("只能撤回自己的消息")
	}

	// 验证撤回时间限制（例如：只能撤回2分钟内的消息）
	if time.Since(message.CreatedAt) > 2*time.Minute {
		return nil, errors.New("消息发送超过2分钟，无法撤回")
	}

	// 验证消息状态
	if message.IsRevoked {
		return nil, errors.New("消息已被撤回")
	}

	// 更新消息状态
	now := time.Now()
	updates := map[string]interface{}{
		"is_revoked": true,
		"revoked_at": now,
		"status":     model.ChatMessageStatusRevoked,
	}

	if err := s.messageRepo.Update(ctx, req.MessageID, updates); err != nil {
		return nil, fmt.Errorf("撤回消息失败: %w", err)
	}

	// TODO: 异步处理撤回通知推送
	// go s.pushRevokeNotification(ctx, message)

	return &types.RevokeMessageResponse{
		Success:   true,
		RevokedAt: now,
	}, nil
}

// GetMessageStatus 获取消息状态
func (s *messageService) GetMessageStatus(ctx context.Context, messageID uint) (string, error) {
	message, err := s.messageRepo.GetByID(ctx, messageID)
	if err != nil {
		return "", fmt.Errorf("获取消息失败: %w", err)
	}

	return string(message.Status), nil
}
