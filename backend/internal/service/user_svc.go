package service

import (
	"context"
	"errors"
	"fmt"

	"bdb-backend/internal/repository"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/wechat"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// UserService 用户服务接口
type UserService interface {
	// 用户资料管理
	GetUserProfile(ctx context.Context, userID uint) (*types.UserProfileResponse, error)
	UpdateUserProfile(ctx context.Context, userID uint, req *types.UpdateUserProfileRequest) error

	// 用户设置
	UpdateUserSettings(ctx context.Context, userID uint, req *types.UpdateUserSettingsRequest) error
	GetUserSettings(ctx context.Context, userID uint) (*types.GetUserSettingsResponse, error)

	// 手机号绑定和更换
	BindPhone(ctx context.Context, userID uint, req *types.BindPhoneRequest) (*types.BindPhoneResponse, error)
	ChangePhoneBySMS(ctx context.Context, userID uint, req *types.ChangePhoneBySMSRequest) error
	ChangePhoneByWechat(ctx context.Context, userID uint, req *types.ChangePhoneByWechatRequest) error
}

// userService 用户服务实现
type userService struct {
	userRepo      repository.UserRepository
	wechatService wechat.AuthService
}

// NewUserService 创建用户服务
func NewUserService(
	userRepo repository.UserRepository,
	wechatService wechat.AuthService,
) UserService {
	return &userService{
		userRepo:      userRepo,
		wechatService: wechatService,
	}
}

// GetUserProfile 获取用户资料
func (s *userService) GetUserProfile(ctx context.Context, userID uint) (*types.UserProfileResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 构建响应
	response := &types.UserProfileResponse{
		UserInfo: types.UserBasicInfo{
			UID:       user.UID,
			Nickname:  user.Nickname,
			Avatar:    user.Avatar,
			Gender:    user.Gender,
			Birthday:  user.Birthday,
			Phone:     user.Phone,
			Status:    user.Status,
			CreatedAt: user.CreatedAt,
		},
		MemberInfo: types.UserMemberInfo{
			MemberLevel:    0,   // 已移至企业表
			MemberExpireAt: nil, // 已移至企业表
			Points:         user.Points,
		},
		Verification: types.UserVerificationInfo{
			IsVerified: user.IsVerified,
		},
		Settings: types.UserSettingsInfo{
			AllowMessageFrom:    1,
			AllowLocationShare:  true,
			AllowJobRecommend:   true,
			AllowHouseRecommend: true,
		},
	}

	return response, nil
}

// UpdateUserProfile 更新用户资料
func (s *userService) UpdateUserProfile(ctx context.Context, userID uint, req *types.UpdateUserProfileRequest) error {
	// 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 更新用户基本信息
	updated := false
	if req.Nickname != "" && req.Nickname != user.Nickname {
		user.Nickname = req.Nickname
		updated = true
	}
	if req.Avatar != "" && req.Avatar != user.Avatar {
		user.Avatar = req.Avatar
		updated = true
	}
	if req.Gender > 0 && req.Gender != user.Gender {
		user.Gender = req.Gender
		updated = true
	}
	if req.Birthday != nil && (user.Birthday == nil || !req.Birthday.Equal(*user.Birthday)) {
		user.Birthday = req.Birthday
		updated = true
	}

	if updated {
		if err := s.userRepo.Update(user); err != nil {
			return fmt.Errorf("更新用户基本信息失败: %w", err)
		}
	}

	// TODO: 处理UserProfile和UserSettings的更新
	// 这里需要添加对应的Repository方法

	return nil
}

// UpdateUserSettings 更新用户设置
func (s *userService) UpdateUserSettings(ctx context.Context, userID uint, req *types.UpdateUserSettingsRequest) error {
	// TODO: 实现用户设置更新逻辑
	// 需要添加UserSettings相关的Repository方法
	return nil
}

// GetUserSettings 获取用户设置
func (s *userService) GetUserSettings(ctx context.Context, userID uint) (*types.GetUserSettingsResponse, error) {
	// TODO: 实现获取用户设置逻辑
	// 需要添加UserSettings相关的Repository方法

	// 返回默认设置
	return &types.GetUserSettingsResponse{
		Settings: types.UserSettingsInfo{
			AllowMessageFrom:    1,
			AllowLocationShare:  true,
			AllowJobRecommend:   true,
			AllowHouseRecommend: true,
		},
	}, nil
}

// BindPhone 绑定手机号
func (s *userService) BindPhone(ctx context.Context, userID uint, req *types.BindPhoneRequest) (*types.BindPhoneResponse, error) {
	// 1. 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		log.Error().Err(err).Uint("user_id", userID).Msg("Failed to get user info")
		return nil, fmt.Errorf("获取用户信息失败: %w", err)
	}

	// 2. 检查用户是否已绑定手机号
	if user.Phone != "" {
		return &types.BindPhoneResponse{
			Success: false,
			Message: "用户已绑定手机号",
			Phone:   s.maskPhone(user.Phone),
		}, nil
	}

	// 3. 通过微信phoneCode获取手机号
	phoneInfo, err := s.wechatService.GetPhoneNumber(ctx, req.PhoneCode)
	if err != nil {
		log.Error().Err(err).Str("phone_code", req.PhoneCode).Msg("Failed to get wechat phone number")
		return nil, errors.New("获取手机号失败")
	}

	// 4. 检查手机号是否已被其他用户使用
	existingUser, err := s.userRepo.GetByPhone(phoneInfo.PurePhoneNumber)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Error().Err(err).Str("phone", phoneInfo.PurePhoneNumber).Msg("Failed to check existing user by phone")
		return nil, errors.New("检查手机号失败")
	}

	if existingUser != nil {
		return &types.BindPhoneResponse{
			Success: false,
			Message: "该手机号已被其他用户使用",
			Phone:   "",
		}, nil
	}

	// 5. 更新用户手机号
	user.Phone = phoneInfo.PurePhoneNumber
	if err := s.userRepo.Update(user); err != nil {
		log.Error().Err(err).Uint("user_id", userID).Msg("Failed to update user phone")
		return nil, errors.New("绑定手机号失败")
	}

	log.Info().Uint("user_id", userID).Str("phone", phoneInfo.PurePhoneNumber).Msg("Phone number bound successfully")

	return &types.BindPhoneResponse{
		Success: true,
		Message: "手机号绑定成功",
		Phone:   s.maskPhone(phoneInfo.PurePhoneNumber),
	}, nil
}

// ChangePhoneBySMS 通过短信更换手机号
func (s *userService) ChangePhoneBySMS(ctx context.Context, userID uint, req *types.ChangePhoneBySMSRequest) error {
	// TODO: 实现通过短信更换手机号逻辑
	// 1. 验证短信验证码
	// 2. 检查新手机号是否已被使用
	// 3. 更新用户手机号
	// 4. 记录更换历史

	return nil
}

// ChangePhoneByWechat 通过微信更换手机号
func (s *userService) ChangePhoneByWechat(ctx context.Context, userID uint, req *types.ChangePhoneByWechatRequest) error {
	// TODO: 实现通过微信更换手机号逻辑
	// 1. 解密微信手机号数据
	// 2. 检查新手机号是否已被使用
	// 3. 更新用户手机号
	// 4. 记录更换历史

	return nil
}

// maskPhone 掩码手机号显示
func (s *userService) maskPhone(phone string) string {
	if len(phone) != 11 {
		return phone
	}
	return phone[:3] + "****" + phone[7:]
}
