package types

// CreateJobRequest 创建职位请求
type CreateJobRequest struct {
	Title       string `json:"title" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"required,min=10"`
	Company     string `json:"company" validate:"required"`
	Location    string `json:"location" validate:"required"`
	Salary      string `json:"salary" validate:"required"`
}

// UpdateJobRequest 更新职位请求
type UpdateJobRequest struct {
	Title       *string `json:"title" validate:"omitempty,min=2,max=50"`
	Description *string `json:"description" validate:"omitempty,min=10"`
	Company     *string `json:"company" validate:"omitempty"`
	Location    *string `json:"location" validate:"omitempty"`
	Salary      *string `json:"salary" validate:"omitempty"`
}
