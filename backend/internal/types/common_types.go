package types

// PaginationReq 分页请求参数
type PaginationReq struct {
	Page     int `form:"page,default=1"`
	PageSize int `form:"pageSize,default=20"`
}

// Sanitize 修正分页参数, 设置默认值和最大值
func (p *PaginationReq) Sanitize() {
	if p.Page <= 0 {
		p.Page = 1
	}

	switch {
	case p.PageSize > 100:
		p.PageSize = 100
	case p.PageSize <= 0:
		p.PageSize = 20
	}
}

// UploadTokenResponse represents the response for getting an upload token
type UploadTokenResponse struct {
	Token  string `json:"token"`
	Domain string `json:"domain"`
}
