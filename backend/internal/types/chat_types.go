package types

import (
	"time"

	"gorm.io/datatypes"
)

// =====================================================
// 聊天系统 API 类型定义
// 用于前后端接口的请求和响应结构体
// =====================================================

// ChatMessageType 聊天消息类型
type ChatMessageType string

const (
	ChatMessageTypeText   ChatMessageType = "text"   // 文本消息
	ChatMessageTypeImage  ChatMessageType = "image"  // 图片消息
	ChatMessageTypeVoice  ChatMessageType = "voice"  // 语音消息
	ChatMessageTypeVideo  ChatMessageType = "video"  // 视频消息
	ChatMessageTypeFile   ChatMessageType = "file"   // 文件消息
	ChatMessageTypeSystem ChatMessageType = "system" // 系统消息
)

// ChatMessageStatus 聊天消息状态
type ChatMessageStatus string

const (
	ChatMessageStatusSent      ChatMessageStatus = "sent"      // 已发送到服务器
	ChatMessageStatusDelivered ChatMessageStatus = "delivered" // 已送达接收方
	ChatMessageStatusRead      ChatMessageStatus = "read"      // 已被阅读
	ChatMessageStatusRevoked   ChatMessageStatus = "revoked"   // 已撤回
	ChatMessageStatusFailed    ChatMessageStatus = "failed"    // 发送失败
)

// ConversationType 会话类型
type ConversationType string

const (
	ConversationTypeSingle ConversationType = "single" // 单聊
	ConversationTypeSystem ConversationType = "system" // 系统通知
)

// =====================================================
// 发送消息相关
// =====================================================

// SendMessageRequest 发送消息请求
type SendMessageRequest struct {
	MsgID       string          `json:"msg_id" validate:"required" comment:"客户端生成的全局唯一消息ID"`
	SenderID    uint            `json:"sender_id" validate:"required" comment:"发送者用户ID"`
	ReceiverID  uint            `json:"receiver_id" validate:"required" comment:"接收者用户ID"`
	Content     string          `json:"content" validate:"required" comment:"消息内容"`
	MessageType ChatMessageType `json:"message_type" validate:"required,oneof=text image voice video file system" comment:"消息类型"`
	Extra       *datatypes.JSON `json:"extra,omitempty" comment:"扩展信息，如图片尺寸、语音时长等"`
}

// SendMessageResponse 发送消息响应
type SendMessageResponse struct {
	MessageID      uint      `json:"message_id" comment:"服务器生成的消息ID"`
	MsgID          string    `json:"msg_id" comment:"客户端消息ID"`
	ConversationID uint      `json:"conversation_id" comment:"会话ID"`
	Status         string    `json:"status" comment:"消息状态"`
	SentAt         time.Time `json:"sent_at" comment:"消息发送时间"`
}

// =====================================================
// 获取消息列表相关
// =====================================================

// GetMessagesRequest 获取消息列表请求
type GetMessagesRequest struct {
	PaginationReq
	UserID         uint `json:"user_id" validate:"required" comment:"用户ID"`
	ConversationID uint `json:"conversation_id" validate:"required" comment:"会话ID"`
	LastMessageID  uint `json:"last_message_id,omitempty" comment:"最后一条消息ID，用于游标分页"`
}

// MessageInfo 消息信息
type MessageInfo struct {
	ID             uint              `json:"id" comment:"消息ID"`
	MsgID          string            `json:"msg_id" comment:"全局唯一消息ID"`
	ConversationID uint              `json:"conversation_id" comment:"会话ID"`
	SenderID       uint              `json:"sender_id" comment:"发送者ID"`
	Content        string            `json:"content" comment:"消息内容"`
	MessageType    ChatMessageType   `json:"message_type" comment:"消息类型"`
	Extra          *datatypes.JSON   `json:"extra,omitempty" comment:"扩展信息"`
	Status         ChatMessageStatus `json:"status" comment:"消息状态"`
	IsRevoked      bool              `json:"is_revoked" comment:"是否已撤回"`
	RevokedAt      *time.Time        `json:"revoked_at,omitempty" comment:"撤回时间"`
	CreatedAt      time.Time         `json:"created_at" comment:"创建时间"`
	DeliveredAt    *time.Time        `json:"delivered_at,omitempty" comment:"送达时间"`
	ReadAt         *time.Time        `json:"read_at,omitempty" comment:"已读时间"`
	SenderInfo     *UserInfo         `json:"sender_info,omitempty" comment:"发送者信息"`
}

// GetMessagesResponse 获取消息列表响应
type GetMessagesResponse struct {
	Messages   []*MessageInfo `json:"messages" comment:"消息列表"`
	Pagination *Pagination    `json:"pagination" comment:"分页信息"`
}

// =====================================================
// 会话列表相关
// =====================================================

// GetConversationsRequest 获取会话列表请求
type GetConversationsRequest struct {
	PaginationReq
	UserID uint `json:"user_id" validate:"required" comment:"用户ID"`
}

// GetConversationListRequest 获取会话列表请求（别名）
type GetConversationListRequest = GetConversationsRequest

// GetConversationListResponse 获取会话列表响应（别名）
type GetConversationListResponse = GetConversationsResponse

// ConversationInfo 会话信息
type ConversationInfo struct {
	ID             uint             `json:"id" comment:"会话ID"`
	Type           ConversationType `json:"type" comment:"会话类型"`
	OtherUser      *UserInfo        `json:"other_user,omitempty" comment:"对方用户信息（单聊时）"`
	CustomName     string           `json:"custom_name,omitempty" comment:"自定义会话名称"`
	LastMessage    *MessageInfo     `json:"last_message,omitempty" comment:"最后一条消息"`
	UnreadCount    int              `json:"unread_count" comment:"未读消息数"`
	IsPinned       bool             `json:"is_pinned" comment:"是否置顶"`
	IsMuted        bool             `json:"is_muted" comment:"是否静音"`
	LastActiveTime time.Time        `json:"last_active_time" comment:"最后活跃时间"`
	CreatedAt      time.Time        `json:"created_at" comment:"会话创建时间"`
}

// GetConversationsResponse 获取会话列表响应
type GetConversationsResponse struct {
	Conversations []*ConversationInfo `json:"conversations" comment:"会话列表"`
	Total         int64               `json:"total" comment:"总记录数"`
	Page          int                 `json:"page" comment:"当前页码"`
	PageSize      int                 `json:"page_size" comment:"每页大小"`
	Pagination    *Pagination         `json:"pagination" comment:"分页信息"`
}

// =====================================================
// 消息操作相关
// =====================================================

// RevokeMessageRequest 撤回消息请求
type RevokeMessageRequest struct {
	UserID    uint `json:"user_id" validate:"required" comment:"用户ID"`
	MessageID uint `json:"message_id" validate:"required" comment:"消息ID"`
}

// RevokeMessageResponse 撤回消息响应
type RevokeMessageResponse struct {
	Success   bool      `json:"success" comment:"是否成功"`
	MessageID uint      `json:"message_id" comment:"消息ID"`
	RevokedAt time.Time `json:"revoked_at" comment:"撤回时间"`
}

// MarkAsReadRequest 标记已读请求
type MarkAsReadRequest struct {
	UserID         uint `json:"user_id" validate:"required" comment:"用户ID"`
	ConversationID uint `json:"conversation_id" validate:"required" comment:"会话ID"`
	MessageID      uint `json:"message_id,omitempty" comment:"标记到此消息为已读，不传则标记所有未读为已读"`
}

// MarkAsReadResponse 标记已读响应
type MarkAsReadResponse struct {
	Success           bool      `json:"success" comment:"是否成功"`
	LastReadMessageID uint      `json:"last_read_message_id" comment:"最后已读消息ID"`
	LastReadTime      time.Time `json:"last_read_time" comment:"最后已读时间"`
}

// MarkMessagesAsReadRequest 批量标记消息为已读请求
type MarkMessagesAsReadRequest struct {
	UserID         uint `json:"user_id" validate:"required" comment:"用户ID"`
	ConversationID uint `json:"conversation_id" validate:"required" comment:"会话ID"`
}

// MarkMessagesAsReadResponse 批量标记消息为已读响应
type MarkMessagesAsReadResponse struct {
	Success   bool `json:"success" comment:"是否成功"`
	ReadCount int  `json:"read_count" comment:"已读消息数量"`
}

// GetMessageListRequest 获取消息列表请求
type GetMessageListRequest struct {
	PaginationReq
	UserID         uint `json:"user_id" validate:"required" comment:"用户ID"`
	ConversationID uint `json:"conversation_id" validate:"required" comment:"会话ID"`
}

// GetMessageListResponse 获取消息列表响应
type GetMessageListResponse struct {
	Messages []*MessageInfo `json:"messages" comment:"消息列表"`
	Total    int64          `json:"total" comment:"总记录数"`
	Page     int            `json:"page" comment:"当前页码"`
	PageSize int            `json:"page_size" comment:"每页大小"`
}

// UpdateMessageStatusRequest 更新消息状态请求（用于消息回执）
type UpdateMessageStatusRequest struct {
	MessageID  uint              `json:"message_id" validate:"required" comment:"消息ID"`
	ReceiverID uint              `json:"receiver_id" validate:"required" comment:"接收者ID"`
	Status     ChatMessageStatus `json:"status" validate:"required,oneof=delivered read" comment:"消息状态"`
}

// UpdateMessageStatusResponse 更新消息状态响应
type UpdateMessageStatusResponse struct {
	Success   bool              `json:"success" comment:"是否成功"`
	MessageID uint              `json:"message_id" comment:"消息ID"`
	Status    ChatMessageStatus `json:"status" comment:"更新后的状态"`
	UpdatedAt time.Time         `json:"updated_at" comment:"更新时间"`
}

// =====================================================
// 会话操作相关
// =====================================================

// DeleteConversationRequest 删除会话请求
type DeleteConversationRequest struct {
	UserID         uint `json:"user_id" validate:"required" comment:"用户ID"`
	ConversationID uint `json:"conversation_id" validate:"required" comment:"会话ID"`
}

// DeleteConversationResponse 删除会话响应
type DeleteConversationResponse struct {
	Success   bool      `json:"success" comment:"是否成功"`
	DeletedAt time.Time `json:"deleted_at" comment:"删除时间"`
}

// PinConversationRequest 置顶会话请求
type PinConversationRequest struct {
	UserID         uint `json:"user_id" validate:"required" comment:"用户ID"`
	ConversationID uint `json:"conversation_id" validate:"required" comment:"会话ID"`
	IsPinned       bool `json:"is_pinned" comment:"是否置顶"`
}

// PinConversationResponse 置顶会话响应
type PinConversationResponse struct {
	Success  bool `json:"success" comment:"是否成功"`
	IsPinned bool `json:"is_pinned" comment:"置顶状态"`
}

// MuteConversationRequest 静音会话请求
type MuteConversationRequest struct {
	UserID         uint `json:"user_id" validate:"required" comment:"用户ID"`
	ConversationID uint `json:"conversation_id" validate:"required" comment:"会话ID"`
	IsMuted        bool `json:"is_muted" comment:"是否静音"`
}

// MuteConversationResponse 静音会话响应
type MuteConversationResponse struct {
	Success bool `json:"success" comment:"是否成功"`
	IsMuted bool `json:"is_muted" comment:"静音状态"`
}

// =====================================================
// 系统通知相关
// =====================================================

// SendSystemNotificationRequest 发送系统通知请求
type SendSystemNotificationRequest struct {
	UserIDs []uint          `json:"user_ids" validate:"required,min=1" comment:"接收用户ID列表"`
	Title   string          `json:"title" validate:"required,min=1,max=100" comment:"通知标题"`
	Content string          `json:"content" validate:"required,min=1,max=500" comment:"通知内容"`
	Extra   *datatypes.JSON `json:"extra,omitempty" comment:"扩展信息"`
}

// SendSystemNotificationResponse 发送系统通知响应
type SendSystemNotificationResponse struct {
	Success     bool   `json:"success" comment:"是否成功"`
	MessageIDs  []uint `json:"message_ids" comment:"发送成功的消息ID列表"`
	FailedCount int    `json:"failed_count" comment:"发送失败的数量"`
}

// SendNotificationRequest 发送通知请求
type SendNotificationRequest struct {
	UserID  uint            `json:"user_id" validate:"required" comment:"接收用户ID"`
	Type    string          `json:"type" validate:"required" comment:"通知类型"`
	Title   string          `json:"title" validate:"required,min=1,max=100" comment:"通知标题"`
	Content string          `json:"content" validate:"required,min=1,max=500" comment:"通知内容"`
	Extra   *datatypes.JSON `json:"extra,omitempty" comment:"扩展信息"`
}

// SendNotificationResponse 发送通知响应
type SendNotificationResponse struct {
	Success        bool      `json:"success" comment:"是否成功"`
	NotificationID uint      `json:"notification_id" comment:"通知ID"`
	SentAt         time.Time `json:"sent_at" comment:"发送时间"`
}

// GetNotificationListRequest 获取通知列表请求
type GetNotificationListRequest struct {
	PaginationReq
	UserID uint   `json:"user_id" validate:"required" comment:"用户ID"`
	Type   string `json:"type,omitempty" comment:"通知类型筛选"`
}

// GetNotificationListResponse 获取通知列表响应
type GetNotificationListResponse struct {
	Notifications []*NotificationInfo `json:"notifications" comment:"通知列表"`
	Total         int64               `json:"total" comment:"总记录数"`
	Page          int                 `json:"page" comment:"当前页码"`
	PageSize      int                 `json:"page_size" comment:"每页大小"`
}

// NotificationInfo 通知信息
type NotificationInfo struct {
	ID        uint            `json:"id" comment:"通知ID"`
	Type      string          `json:"type" comment:"通知类型"`
	Title     string          `json:"title" comment:"通知标题"`
	Content   string          `json:"content" comment:"通知内容"`
	IsRead    bool            `json:"is_read" comment:"是否已读"`
	Extra     *datatypes.JSON `json:"extra,omitempty" comment:"扩展信息"`
	CreatedAt time.Time       `json:"created_at" comment:"创建时间"`
	ReadAt    *time.Time      `json:"read_at,omitempty" comment:"已读时间"`
}

// MarkNotificationAsReadRequest 标记通知为已读请求
type MarkNotificationAsReadRequest struct {
	UserID         uint `json:"user_id" validate:"required" comment:"用户ID"`
	NotificationID uint `json:"notification_id" validate:"required" comment:"通知ID"`
}

// MarkNotificationAsReadResponse 标记通知为已读响应
type MarkNotificationAsReadResponse struct {
	Success bool      `json:"success" comment:"是否成功"`
	ReadAt  time.Time `json:"read_at" comment:"已读时间"`
}

// MarkAllNotificationsAsReadRequest 标记所有通知为已读请求
type MarkAllNotificationsAsReadRequest struct {
	UserID uint `json:"user_id" validate:"required" comment:"用户ID"`
}

// MarkAllNotificationsAsReadResponse 标记所有通知为已读响应
type MarkAllNotificationsAsReadResponse struct {
	Success   bool      `json:"success" comment:"是否成功"`
	ReadCount int       `json:"read_count" comment:"已读通知数量"`
	ReadAt    time.Time `json:"read_at" comment:"批量已读时间"`
}

// =====================================================
// 实时推送事件
// =====================================================

// RealtimeEvent 实时推送事件
type RealtimeEvent struct {
	Type string      `json:"type" comment:"事件类型"`
	Data interface{} `json:"data" comment:"事件数据"`
}

// NewMessageEvent 新消息事件
type NewMessageEvent struct {
	Type         string            `json:"type" comment:"事件类型：new_message"`
	Message      *MessageInfo      `json:"message" comment:"消息信息"`
	Conversation *ConversationInfo `json:"conversation" comment:"会话信息"`
}

// MessageStatusUpdateEvent 消息状态更新事件
type MessageStatusUpdateEvent struct {
	Type      string            `json:"type" comment:"事件类型：message_status_update"`
	MessageID uint              `json:"message_id" comment:"消息ID"`
	Status    ChatMessageStatus `json:"status" comment:"新状态"`
	UpdatedAt time.Time         `json:"updated_at" comment:"更新时间"`
}

// MessageRevokeEvent 消息撤回事件
type MessageRevokeEvent struct {
	Type           string    `json:"type" comment:"事件类型：message_revoke"`
	MessageID      uint      `json:"message_id" comment:"消息ID"`
	ConversationID uint      `json:"conversation_id" comment:"会话ID"`
	RevokedAt      time.Time `json:"revoked_at" comment:"撤回时间"`
}

// ConversationUpdateEvent 会话更新事件
type ConversationUpdateEvent struct {
	Type         string            `json:"type" comment:"事件类型：conversation_update"`
	Conversation *ConversationInfo `json:"conversation" comment:"会话信息"`
}

// =====================================================
// 通用响应结构
// =====================================================

// Pagination 分页信息
type Pagination struct {
	Page       int   `json:"page" comment:"当前页码"`
	Size       int   `json:"size" comment:"每页大小"`
	Total      int64 `json:"total" comment:"总记录数"`
	TotalPages int   `json:"total_pages" comment:"总页数"`
	HasNext    bool  `json:"has_next" comment:"是否有下一页"`
}

// 复用已有的UserInfo类型（来自auth_types.go）
// type UserInfo struct {
// 	UID      string `json:"uid"`
// 	Phone    string `json:"phone"`
// 	Nickname string `json:"nickname"`
// 	Avatar   string `json:"avatar"`
// 	Source   string `json:"source"`
// }
