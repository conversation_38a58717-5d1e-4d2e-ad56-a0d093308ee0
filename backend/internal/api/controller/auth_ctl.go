package controller

import (
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// AuthController 认证控制器
type AuthController struct {
	authService service.AuthService
	validator   validator.Validator
}

// NewAuthController 创建新的认证控制器
func NewAuthController(authService service.AuthService, validator validator.Validator) *AuthController {
	return &AuthController{
		authService: authService,
		validator:   validator,
	}
}

// WechatLogin 微信登录
func (c *AuthController) WechatLogin(ctx *gin.Context) {
	var req types.WechatLoginRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	resp, err := c.authService.WechatLogin(ctx, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}
	response.OK(ctx, resp)
}

// Login 短信登录
func (c *AuthController) Login(ctx *gin.Context) {
	var req types.LoginRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	resp, err := c.authService.Login(ctx, &req)
	if err != nil {
		logger.Error("短信登录失败", err)
		response.Unauthorized(ctx, err.Error())
		return
	}
	response.OK(ctx, resp)
}

// SendSmsCode 发送短信验证码
func (c *AuthController) SendSmsCode(ctx *gin.Context) {
	var req types.SendSmsCodeRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	if err := c.authService.SendSmsCode(ctx, &req); err != nil {
		logger.ErrorCtx(ctx, "发送短信验证码失败", err)
		response.ServerError(ctx, err.Error())
		return
	}
	response.OK(ctx, nil)
}

// CheckUserByCode 根据微信code检查用户是否注册
func (c *AuthController) CheckUserByCode(ctx *gin.Context) {
	var req types.CheckUserByCodeRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	resp, err := c.authService.CheckUserByCode(ctx, &req)
	if err != nil {
		response.ServerError(ctx, err.Error())
		return
	}

	response.OK(ctx, resp)
}
