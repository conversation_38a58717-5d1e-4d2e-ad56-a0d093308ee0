package controller

import (
	"time"

	"bdb-backend/internal/model"
	"bdb-backend/internal/service"
	"bdb-backend/internal/types"
	"bdb-backend/pkg/logger"
	"bdb-backend/pkg/response"
	"bdb-backend/pkg/validator"

	"github.com/gin-gonic/gin"
)

// NotificationController 通知控制器
type NotificationController struct {
	notificationService service.NotificationService
	validator           validator.Validator
}

// NewNotificationController 创建通知控制器
func NewNotificationController(notificationService service.NotificationService, validator validator.Validator) *NotificationController {
	return &NotificationController{
		notificationService: notificationService,
		validator:           validator,
	}
}

// SendNotification 发送系统通知
func (c *NotificationController) SendNotification(ctx *gin.Context) {
	var req types.SendSystemNotificationRequest
	if !c.validator.CheckJSON(ctx, &req) {
		return
	}

	resp, err := c.notificationService.SendSystemNotification(ctx, &req)
	if err != nil {
		logger.ErrorCtx(ctx, "发送系统通知失败", err, "user_ids", req.UserIDs, "title", req.Title)
		response.ServerError(ctx, "发送系统通知失败")
		return
	}

	response.OK(ctx, resp)
}

// GetNotifications 获取通知列表
func (c *NotificationController) GetNotifications(ctx *gin.Context) {
	userID := GetUserID(ctx)

	var req types.GetNotificationListRequest
	if !c.validator.CheckQuery(ctx, &req) {
		return
	}

	// 调用service方法
	var notificationType model.NotificationType
	// TODO: 需要根据req.Type设置正确的通知类型
	notifications, total, err := c.notificationService.GetUserNotifications(ctx, userID, req.Page, req.PageSize, notificationType)
	if err != nil {
		logger.ErrorCtx(ctx, "获取通知列表失败", err, "user_id", userID)
		response.ServerError(ctx, "获取通知列表失败")
		return
	}

	// 构建响应
	resp := &types.GetNotificationListResponse{
		Notifications: make([]*types.NotificationInfo, 0, len(notifications)),
		Total:         total,
		Page:          req.Page,
		PageSize:      req.PageSize,
	}

	// 转换为前端需要的格式
	for _, notification := range notifications {
		notificationInfo := &types.NotificationInfo{
			ID:        notification.ID,
			Type:      string(notification.Type),
			Title:     notification.Title,
			Content:   notification.Content,
			IsRead:    notification.IsRead(),
			CreatedAt: notification.CreatedAt,
		}
		if notification.ReadAt != nil {
			notificationInfo.ReadAt = notification.ReadAt
		}
		resp.Notifications = append(resp.Notifications, notificationInfo)
	}

	response.OK(ctx, resp)
}

// MarkNotificationAsRead 标记通知为已读
func (c *NotificationController) MarkNotificationAsRead(ctx *gin.Context) {
	userID := GetUserID(ctx)

	notificationID := ParseUintParam(ctx, "id")
	if notificationID == 0 {
		return
	}

	err := c.notificationService.MarkAsRead(ctx, userID, notificationID)
	if err != nil {
		logger.ErrorCtx(ctx, "标记通知已读失败", err, "user_id", userID, "notification_id", notificationID)
		response.ServerError(ctx, "标记通知已读失败")
		return
	}

	resp := &types.MarkNotificationAsReadResponse{
		Success: true,
		ReadAt:  time.Now(),
	}

	response.OK(ctx, resp)
}

// GetUnreadCount 获取未读通知数量
func (c *NotificationController) GetUnreadCount(ctx *gin.Context) {
	userID := GetUserID(ctx)

	count, err := c.notificationService.GetUnreadCount(ctx, userID)
	if err != nil {
		logger.ErrorCtx(ctx, "获取未读通知数量失败", err, "user_id", userID)
		response.ServerError(ctx, "获取未读通知数量失败")
		return
	}

	response.OK(ctx, gin.H{"unread_count": count})
}

// MarkAllAsRead 标记所有通知为已读
func (c *NotificationController) MarkAllAsRead(ctx *gin.Context) {
	userID := GetUserID(ctx)

	err := c.notificationService.MarkAllAsRead(ctx, userID)
	if err != nil {
		logger.ErrorCtx(ctx, "标记所有通知已读失败", err, "user_id", userID)
		response.ServerError(ctx, "标记所有通知已读失败")
		return
	}

	resp := &types.MarkAllNotificationsAsReadResponse{
		Success: true,
		ReadAt:  time.Now(),
		// TODO: 可以通过service返回实际标记为已读的数量
		ReadCount: 0,
	}

	response.OK(ctx, resp)
}
