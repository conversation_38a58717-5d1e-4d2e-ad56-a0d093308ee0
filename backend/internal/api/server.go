package api

import (
	"bdb-backend/internal/api/controller"
	"bdb-backend/internal/api/router"
	"bdb-backend/internal/scheduler"
	"bdb-backend/pkg/cache"
	"bdb-backend/pkg/config"
	"bdb-backend/pkg/database"
	"bdb-backend/pkg/jwt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Server struct {
	router    *gin.Engine
	config    *config.Config
	db        *gorm.DB
	cache     cache.Cache
	scheduler scheduler.Scheduler
}

// NewDB creates a new database connection.
func NewDB(cfg *config.Config) *gorm.DB {
	return database.NewPostgresClient(cfg.Database)
}

// NewCache creates a new cache instance.
func NewCache(cfg *config.Config) cache.Cache {
	return cache.NewRedisCache(cfg.Redis)
}

// NewServer creates a new server instance.
func NewServer(
	cfg *config.Config,
	db *gorm.DB,
	cache cache.Cache,
	authCtl *controller.AuthController,
	userCtl *controller.UserController,
	chatCtl *controller.ChatController,
	messageCtl *controller.MessageController,
	notificationCtl *controller.NotificationController,
	gigCtl *controller.GigController,
	commonCtl *controller.CommonController,
	scheduler scheduler.Scheduler,
	jwtService jwt.JWTService,
) *Server {
	// Controllers for features not yet fully implemented

	r := router.SetupRouter(cfg, authCtl, userCtl, gigCtl, messageCtl, chatCtl, notificationCtl, commonCtl, jwtService)

	return &Server{
		router:    r,
		config:    cfg,
		db:        db,
		cache:     cache,
		scheduler: scheduler,
	}
}

func (s *Server) Run() error {
	// 启动定时调度器
	go s.scheduler.Start()

	return s.router.Run(":" + s.config.Server.Port)
}

func (s *Server) Close() error {
	// 停止调度器
	if s.scheduler != nil {
		s.scheduler.Stop()
	}

	sqlDB, err := s.db.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
