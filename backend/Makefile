# Makefile
.PHONY: build run dev test clean docker-build docker-run

# 项目名称
PROJECT_NAME=bdb-backend
BINARY_NAME=server

# 构建
build:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/$(BINARY_NAME) cmd/server/main.go

# 运行
run:
	go run cmd/server/main.go

# 开发模式（热重载）
dev:
	air -c .air.toml

# 测试
test:
	go test -v ./...

# 清理
clean:
	rm -rf bin/

# Docker构建
docker-build:
	docker build -t $(PROJECT_NAME):latest .

# Docker运行
docker-run:
	docker-compose up -d

# 停止Docker
docker-stop:
	docker-compose down

# 数据库迁移
migrate-up:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/bdb?sslmode=disable" up

migrate-down:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/bdb?sslmode=disable" down

# ===== Go 官方工具 - 代码质量检查 =====

# 代码格式化（官方工具）
fmt:
	@echo "🎨 格式化代码..."
	go fmt ./...
	@echo "✅ 代码格式化完成"

# 导入整理（推荐安装）
imports:
	@echo "📦 整理导入..."
	@which goimports > /dev/null || (echo "安装 goimports..." && go install golang.org/x/tools/cmd/goimports@latest)
	goimports -w .
	@echo "✅ 导入整理完成"

# 代码检查（官方工具）
vet:
	@echo "🔍 代码静态检查..."
	go vet ./...
	@echo "✅ 静态检查完成"

# 代码修复（实验性）
fix:
	@echo "🔧 自动修复代码..."
	go fix ./...
	@echo "✅ 代码修复完成"

# 模块整理
tidy:
	@echo "📚 整理模块依赖..."
	go mod tidy
	@echo "✅ 模块依赖整理完成"

# 安全检查（可选，需要安装）
sec:
	@echo "🔒 安全检查..."
	@which gosec > /dev/null || (echo "安装 gosec..." && go install github.com/securego/gosec/v2/cmd/gosec@latest)
	@gosec -quiet ./... || echo "⚠️  发现安全问题，请检查"

# 静态分析检查（推荐）
staticcheck:
	@echo "🔍 静态分析检查..."
	@which staticcheck > /dev/null || (echo "安装 staticcheck..." && go install honnef.co/go/tools/cmd/staticcheck@latest)
	@staticcheck ./... || echo "⚠️  发现静态分析问题，请检查"

# 简单的错误检查（可选，需要安装）
errcheck:
	@echo "❌ 错误处理检查..."
	@which errcheck > /dev/null || (echo "安装 errcheck..." && go install github.com/kisielk/errcheck@latest)
	@errcheck ./... || echo "⚠️  发现未处理的错误，请检查"

# 一键质量检查（推荐日常使用）
check: fmt imports vet tidy
	@echo "🎉 代码质量检查完成！"

# 完整质量检查（包含静态分析、安全和错误检查）
check-all: fmt imports vet staticcheck fix tidy sec errcheck
	@echo "🎉 完整代码质量检查完成！"

# 安装推荐的开发工具
install-tools:
	@echo "📦 安装 Go 开发工具..."
	go install golang.org/x/tools/cmd/goimports@latest
	go install honnef.co/go/tools/cmd/staticcheck@latest
	go install github.com/securego/gosec/v2/cmd/gosec@latest
	go install github.com/kisielk/errcheck@latest
	go install github.com/cosmtrek/air@latest
	@echo "✅ 开发工具安装完成"

# 代码覆盖率测试
test-coverage:
	@echo "📊 生成测试覆盖率报告..."
	go test -v -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "✅ 覆盖率报告生成: coverage.html"

# 基准测试
benchmark:
	go test -bench=. -benchmem ./...

# 生成API文档
docs:
	swag init -g cmd/server/main.go -o ./docs

# 安装依赖
deps:
	go mod tidy
	go mod download

# 验证依赖
verify:
	go mod verify

# 清理模块缓存
clean-cache:
	go clean -modcache 