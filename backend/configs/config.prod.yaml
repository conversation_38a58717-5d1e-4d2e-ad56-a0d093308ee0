server:
  port: "8080"
  mode: "release"
  read_timeout: 30s
  write_timeout: 30s

database:
  host: "${DB_HOST}"
  port: 5432
  user: "${DB_USER}"
  password: "${DB_PASSWORD}"
  dbname: "${DB_NAME}"
  sslmode: "require"
  max_idle_conns: 20
  max_open_conns: 200

redis:
  addr: "${REDIS_ADDR}"
  password: "${REDIS_PASSWORD}"
  db: 0
  pool_size: 20

jwt:
  secret: "${JWT_SECRET}"
  ttl: "24h"

storage:
  qiniu:
    access_key: "${QINIU_ACCESS_KEY}"
    secret_key: "${QINIU_SECRET_KEY}"
    bucket: "${QINIU_BUCKET}"
    domain: "${QINIU_DOMAIN}"

sms:
  aliyun:
    access_key: "${ALIYUN_SMS_ACCESS_KEY}"
    secret_key: "${ALIYUN_SMS_SECRET_KEY}"
    sign_name: "${ALIY<PERSON>_SMS_SIGN_NAME}"
    template_code: "${ALIYUN_SMS_TEMPLATE_CODE}"

payment:
  wechat:
    app_id: "${WECHAT_APP_ID}"
    mch_id: "${WECHAT_MCH_ID}"
    api_key: "${WECHAT_API_KEY}"
  alipay:
    app_id: "${ALIPAY_APP_ID}"
    private_key: "${ALIPAY_PRIVATE_KEY}"
    public_key: "${ALIPAY_PUBLIC_KEY}"

logger:
  level: "warn"
  format: "json"
  output_paths: ["./logs/app.log"]
  error_output_paths: ["./logs/error.log"]
  rotation:
    max_size: 100
    max_age: 30
    max_backups: 10
    compress: true

centrifugo:
  url: "${CENTRIFUGO_URL}"
  api_key: "${CENTRIFUGO_API_KEY}"
  hmac_secret: "${CENTRIFUGO_HMAC_SECRET}"
  grpc_port: "${CENTRIFUGO_GRPC_PORT}"

cors:
  enabled: true
  allowed_origins:
    - "${FRONTEND_URL}"
    - "${ADMIN_URL}"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Origin"
    - "Content-Type"
    - "Authorization"
    - "X-Requested-With"
  allow_credentials: true

rate_limit:
  enabled: true
  requests_per_second: 50
  burst: 100

security:
  bcrypt_cost: 12
  max_login_attempts: 3
  lockout_duration: "1h"
  password_min_length: 8
  password_require_special: true
  password_require_number: true
  password_require_uppercase: true
  password_require_lowercase: true

file_upload:
  max_file_size: 5242880  # 5MB
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "video/mp4"
    - "video/quicktime"
    - "audio/mpeg"
    - "audio/wav"
    - "application/pdf"
    - "application/msword"
    - "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  upload_path: "/data/uploads"

cache:
  enabled: true
  ttl: "30m"
  prefix: "bdb:prod:"

notification:
  enabled: true
  push_service: "jpush"
  jpush:
    app_key: "${JPUSH_APP_KEY}"
    master_secret: "${JPUSH_MASTER_SECRET}"
    apns_production: true

monitoring:
  enabled: true
  prometheus:
    port: "9090"
    path: "/metrics"
  health_check:
    enabled: true
    path: "/health"

swagger:
  enabled: false
  title: "本地宝API"
  description: "本地宝后端API文档"
  version: "1.0.0"
  host: "${API_HOST}"
  base_path: "/api/v1" 