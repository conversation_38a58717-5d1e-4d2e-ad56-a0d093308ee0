# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a local lifestyle O2O service platform targeting tier-3/4 cities and blue-collar workers. The project consists of a frontend uni-app cross-platform application and a Go backend service.

### Core Features
- **Job Recruitment** - Full-time, part-time, and internship job postings
- **Gig Economy** - Temporary work marketplace with order management
- **Real Estate** - Rental, second-hand, and new property listings
- **Dating/Social** - Local community features and user matching
- **Instant Messaging** - Real-time chat system

## Project Structure

```
fnbdb-mini/
├── front/          # Frontend uni-app project (Vue 3 + TypeScript)
│   ├── src/
│   │   ├── pages/        # Page components
│   │   ├── components/   # Reusable components
│   │   ├── stores/       # Pinia state management
│   │   ├── utils/        # Utility functions
│   │   ├── api/          # API interface definitions
│   │   └── types/        # TypeScript type definitions
│   └── package.json
├── backend/        # Go backend service
│   ├── cmd/server/       # Application entry point
│   ├── internal/         # Internal packages
│   │   ├── api/         # API layer (controllers, middleware, routing)
│   │   ├── service/     # Business logic layer
│   │   ├── repository/  # Data access layer
│   │   └── model/       # Data models
│   ├── pkg/             # Public packages
│   └── Makefile
└── .cursor/rules/  # Cursor IDE configuration
```

### Database Design Specifications
- Utilize Postgresql data support types, appropriate field types and index types to enhance query efficiency and performance
- Include audit fields (`created_at`, `updated_at`) if using soft deletion (`is_del` field)
- Prohibit the use of foreign key constraints, ENUM, etc. in tables as they can affect performance or be difficult to maintain
- Use the string type for status fields and apply constraints using CHECK, for example: ``` status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft','recruiting','paused','locked','in_progress','completed','closed')), ```
  Except for fields such as education, gender, and is_del which are globally used and have fixed statuses, smallint types can be used
- In SQL for data tables, try to set NOT NULL and design DEFAULT values
- For fields related to transactions, orders, and product prices in the table, use int types for storage, with units of cents; except for salaries in recruitment ranging from 8000 to 10000, which can use the yuan unit as it does not involve transactions, payments, and bills


## Development Commands

### Frontend (from front/ directory)
```bash
# Development
pnpm dev:mp-weixin    # WeChat Mini Program
pnpm dev:h5           # H5 Web App
pnpm dev:mp-alipay    # Alipay Mini Program

# Build
pnpm build:mp-weixin  # Build WeChat Mini Program
pnpm build:h5         # Build H5 Web App

# Type checking
pnpm type-check       # TypeScript type checking
```

### Backend (from backend/ directory)
```bash
# Development
make dev              # Start with hot reload (using air)
make run              # Start without hot reload

# Build
make build            # Build binary to bin/server

# Code quality
make check            # Basic checks (fmt, imports, vet, tidy)
make check-all        # Complete checks (includes staticcheck, sec, errcheck)

# Testing
make test             # Run all tests
make test-coverage    # Generate test coverage report

# Tools
make install-tools    # Install development tools
```

## Architecture Patterns

### Frontend Architecture
- **Component-based**: Modular Vue 3 components with TypeScript
- **State Management**: Pinia stores for global state
- **API Layer**: Centralized API definitions using alova
- **Utility Functions**: Shared utilities for common operations

### Backend Architecture
- **Layered Architecture**: Controller → Service → Repository → Model
- **Dependency Injection**: Google Wire for dependency management
- **Middleware Pattern**: Unified request processing pipeline
- **Error Handling**: Standardized error response format

### Constants Use
Constants are defined in the "constants" directory. Please try to use the predefined constants in your business logic and follow this practice. This is more convenient for maintenance and ensures standardization.

## Technology Stack

### Frontend
- **Framework**: Vue 3 + TypeScript + uni-app CLI
- **State Management**: Pinia
- **UI Libraries**: uni-ui, uv-ui, ThorUI
- **Network**: alova for API requests
- **Styling**: UnoCSS + SCSS

### Backend
- **Language**: Go 1.24.0
- **Web Framework**: Gin
- **Database**: PostgreSQL + GORM
- **Cache**: Redis
- **Real-time**: Centrifugo
- **Authentication**: JWT
- **Configuration**: Viper

## Key Development Guidelines

### API Design
- Use RESTful principles with snake_case for JSON keys
- Frontend uses camelCase, backend uses snake_case
- All APIs versioned with `/api/v1/` prefix
- Consistent error response format

### Code Quality
- **Backend**: Run `make check` before committing
- **Frontend**: Run `pnpm type-check` to validate TypeScript
- Follow established patterns in existing code
- Use dependency injection for backend services

### Database Design
- Use PostgreSQL with proper indexing
- Include audit fields (created_at, updated_at)
- Use soft deletes with is_del field
- Status fields use VARCHAR with CHECK constraints

## Common Patterns

### Frontend Component Structure
```typescript
// components follow this pattern
interface Props {
  title: string
  count?: number
}

const props = withDefaults(defineProps<Props>(), {
  count: 0
})

const emit = defineEmits<{
  click: [value: string]
}>()
```

### Backend Service Pattern
```go
// services follow this pattern
type ServiceInterface interface {
  GetByID(ctx context.Context, id uint) (*Model, error)
  Create(ctx context.Context, req *CreateRequest) error
}

type service struct {
  repo RepositoryInterface
}
```

## Testing Strategy

### Frontend
- Component testing with Vue Test Utils
- API mocking for unit tests
- E2E testing for critical user flows

### Backend
- Unit tests for service layer
- Integration tests for API endpoints
- Table-driven tests for business logic

## Important Notes

- **Working Directory**: Always ensure you're in the correct subdirectory (front/ or backend/)
- **Dependencies**: Frontend and backend manage dependencies independently
- **Configuration**: Environment-specific config files for different deployment stages
- **Hot Reload**: Use `make dev` for backend and `pnpm dev:mp-weixin` for frontend development