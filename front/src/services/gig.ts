/**
 * 零工业务逻辑服务
 * 包含零工相关的业务判断和操作逻辑
 */
import { GigStatus, GigApplicationStatus } from '@/constants/gig';
import type { Gig, GigApplication } from '@/types/gig';
import dayjs from 'dayjs';

// ====================================================================
// 零工业务判断函数
// ====================================================================

/**
 * 检查零工是否可以申请
 * @param gig 零工信息
 */
export function canApplyForGig(gig: Gig): boolean {
    return gig.status === GigStatus.Recruiting && gig.current_people_count < gig.people_count;
}

/**
 * 检查零工是否可以编辑
 * @param gig 零工信息
 */
export function canEditGig(gig: Gig): boolean {
    return gig.status === GigStatus.Draft || gig.status === GigStatus.Recruiting;
}

/**
 * 检查零工是否可以删除
 * @param gig 零工信息
 */
export function canDeleteGig(gig: Gig): boolean {
    return gig.status === GigStatus.Draft || gig.status === GigStatus.Completed || gig.status === GigStatus.Closed;
}

/**
 * 检查申请是否可以撤回
 * @param application 申请信息
 */
export function canWithdrawApplication(application: GigApplication): boolean {
    return application.status === GigApplicationStatus.Pending;
}

/**
 * 检查申请是否可以取消
 * @param application 申请信息
 */
export function canCancelApplication(application: GigApplication): boolean {
    return application.status === GigApplicationStatus.Confirmed;
}

/**
 * 检查零工是否招满
 * @param gig 零工信息
 */
export function isGigFull(gig: Gig): boolean {
    return gig.current_people_count >= gig.people_count;
}

/**
 * 检查零工是否过期
 * @param gig 零工信息
 */
export function isGigExpired(gig: Gig): boolean {
    return dayjs().isAfter(dayjs(gig.end_time));
}

/**
 * 计算零工的紧急程度
 * @param gig 零工信息
 */
export function getUrgencyLevel(gig: Gig): 'high' | 'medium' | 'low' {
    const now = dayjs();
    const startTime = dayjs(gig.start_time);
    const diffHours = startTime.diff(now, 'hour');

    if (diffHours <= 24) return 'high';
    if (diffHours <= 72) return 'medium';
    return 'low';
}

/**
 * 检查零工是否即将开始（24小时内）
 * @param gig 零工信息
 */
export function isGigStartingSoon(gig: Gig): boolean {
    return getUrgencyLevel(gig) === 'high';
}

/**
 * 获取零工剩余招聘人数
 * @param gig 零工信息
 */
export function getGigRemainingSlots(gig: Gig): number {
    return Math.max(0, gig.people_count - gig.current_people_count);
}

/**
 * 获取零工招聘进度文本
 * @param gig 零工信息
 */
export function getGigRecruitmentProgress(gig: Gig): string {
    return `${gig.current_people_count}/${gig.people_count}`;
}

/**
 * 计算零工招聘进度百分比
 * @param gig 零工信息
 */
export function getGigRecruitmentPercentage(gig: Gig): number {
    if (gig.people_count === 0) return 0;
    return Math.round((gig.current_people_count / gig.people_count) * 100);
}

/**
 * 获取零工状态对应的操作按钮
 * @param gig 零工信息
 * @param isOwner 是否是零工发布者
 */
export function getGigActions(gig: Gig, isOwner: boolean = false) {
    if (isOwner) {
        // 发布者的操作
        switch (gig.status) {
            case GigStatus.Draft:
                return [
                    { text: '发布', action: 'publish', type: 'primary' },
                    { text: '编辑', action: 'edit', type: 'secondary' },
                    { text: '删除', action: 'delete', type: 'danger' }
                ];
            case GigStatus.Recruiting:
                return [
                    { text: '查看申请', action: 'viewApplicants', type: 'primary' },
                    { text: '暂停', action: 'pause', type: 'warning' },
                    { text: '编辑', action: 'edit', type: 'secondary' }
                ];
            case GigStatus.Paused:
                return [
                    { text: '继续招聘', action: 'resume', type: 'success' },
                    { text: '关闭', action: 'close', type: 'danger' }
                ];
            case GigStatus.Locked:
            case GigStatus.InProgress:
                return [
                    { text: '查看申请', action: 'viewApplicants', type: 'primary' },
                    { text: '联系工人', action: 'contact', type: 'success' }
                ];
            case GigStatus.Completed:
            case GigStatus.Closed:
                return [
                    { text: '删除', action: 'delete', type: 'danger' },
                    { text: '再次发布', action: 'republish', type: 'secondary' }
                ];
            default:
                return [];
        }
    } else {
        // 求职者的操作
        if (canApplyForGig(gig)) {
            return [{ text: '立即申请', action: 'apply', type: 'primary' }];
        } else {
            return [{ text: '查看详情', action: 'viewDetail', type: 'secondary' }];
        }
    }
}

// ====================================================================
// 零工业务数据处理函数
// ====================================================================

/**
 * 格式化零工完整地址
 * @param gig 零工信息
 */
export function formatGigAddress(gig: Gig): string {
    if (gig.detail_address) {
        return `${gig.address_name} ${gig.detail_address}`;
    }
    return gig.address_name;
}

/**
 * 获取零工标签列表（包含紧急标签）
 * @param gig 零工信息
 */
export function getGigTags(gig: Gig): string[] {
    const tags = [...(gig.tags || [])];
    if (gig.is_urgent) {
        tags.unshift('紧急');
    }
    return tags;
}

/**
 * 获取零工要求摘要
 * @param gig 零工信息
 */
export function getGigRequirementSummary(gig: Gig): string[] {
    const requirements: string[] = [];
    
    if (gig.experience) {
        requirements.push(`经验要求: ${gig.experience}`);
    }
    
    if (gig.gender) {
        requirements.push(`性别要求: ${gig.gender}`);
    }
    
    if (gig.age_min || gig.age_max) {
        if (gig.age_min && gig.age_max) {
            requirements.push(`年龄: ${gig.age_min}-${gig.age_max}岁`);
        } else if (gig.age_min) {
            requirements.push(`年龄: ${gig.age_min}岁以上`);
        } else if (gig.age_max) {
            requirements.push(`年龄: ${gig.age_max}岁以下`);
        }
    }
    
    return requirements;
}

/**
 * 检查零工是否满足用户条件
 * @param gig 零工信息
 * @param userProfile 用户信息
 */
export function isGigSuitableForUser(gig: Gig, userProfile: any): boolean {
    // 性别要求检查
    if (gig.gender && userProfile.gender && gig.gender !== userProfile.gender) {
        return false;
    }
    
    // 年龄要求检查
    if (userProfile.age) {
        if (gig.age_min && userProfile.age < gig.age_min) {
            return false;
        }
        if (gig.age_max && userProfile.age > gig.age_max) {
            return false;
        }
    }
    
    // 经验要求检查
    if (gig.experience && userProfile.experience && gig.experience > userProfile.experience) {
        return false;
    }
    
    return true;
}

/**
 * 获取零工状态的下一步操作建议
 * @param gig 零工信息
 * @param isOwner 是否是发布者
 */
export function getGigNextStepSuggestion(gig: Gig, isOwner: boolean = false): string {
    if (isOwner) {
        switch (gig.status) {
            case GigStatus.Draft:
                return '完善信息后发布零工';
            case GigStatus.Recruiting:
                return '等待求职者申请或主动邀请';
            case GigStatus.Paused:
                return '重新开始招聘或关闭零工';
            case GigStatus.Locked:
                return '准备开始工作';
            case GigStatus.InProgress:
                return '监督工作进展';
            case GigStatus.Completed:
                return '查看工作结果并评价';
            case GigStatus.Closed:
                return '可以再次发布类似零工';
            default:
                return '';
        }
    } else {
        if (canApplyForGig(gig)) {
            return '立即申请获得工作机会';
        } else if (isGigFull(gig)) {
            return '该零工已招满';
        } else if (isGigExpired(gig)) {
            return '该零工已过期';
        } else {
            return '暂时无法申请';
        }
    }
}