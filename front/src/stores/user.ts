import { defineStore } from "pinia";
import type { UserInfo } from "@/types/user";
import { getUserProfile } from "@/api/user";

export const useUserStore = defineStore("user", {
  state: () => ({
    user: {} as UserInfo,
    accessToken: "" as string,
    loginTime: null as number | null,
  }),

  actions: {
    // 设置用户信息
    setUserInfo(user: UserInfo, accessToken: string) {
      this.user = user;
      this.accessToken = accessToken;
      this.loginTime = Date.now();
    },

    // 清除用户信息
    clearUserInfo() {
      this.user = {} as UserInfo;
      this.accessToken = "";
      this.loginTime = null;
    },

    // 更新用户信息
    updateUserInfo(userInfo: Partial<UserInfo>) {
      if (this.user) {
        this.user = { ...this.user, ...userInfo };
      }
    },

    // 从服务器获取并更新用户信息
    async fetchUserProfile() {
      if (!this.isLogin) return;
      try {
        const res = await getUserProfile();
        if (res.code === 0 && res.data) {
          this.updateUserInfo(res.data);
        }
      } catch (error) {
        // 根据您的要求，此处暂时不处理错误
      }
    },
  },

  getters: {
    // 获取用户信息
    getUser: (state) => state.user,

    // 是否已登录
    isLogin: (state) => !!state.user?.id && !!state.accessToken,

    // 用户显示名称
    displayName: (state) => state.user?.nickname || "用户",

    // 用户头像
    userAvatar: (state) =>
      state.user?.avatar || "/static/images/default-avatar.png",

    // 是否VIP用户
    isVipUser: (state) => !!state.user?.isVip,

    // 是否已认证
    isVerified: (state) => !!state.user?.personalVerified,
  },

  // 启用数据持久化
  persist: true,
});