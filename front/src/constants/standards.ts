// =====================================================
// 数据标准化常量定义
// =====================================================

// 性别标准
export const GENDER = {
  UNLIMITED: 0, // 不限
  MALE: 1,      // 男
  FEMALE: 2     // 女
} as const

// 年龄范围标准 (简化版)
export const AGE_RANGE = {
  UNLIMITED: 0, // 不限
  AGE_18_TO_30: 1, // 18-30岁
  AGE_31_TO_40: 2, // 31-40岁
  AGE_41_TO_50: 3, // 41-50岁
  AGE_50_PLUS: 4   // 50岁以上
} as const

// 学历标准 (移除博士)
export const EDUCATION = {
  UNLIMITED: 0,   // 不限
  JUNIOR_HIGH: 1, // 初中及以下
  HIGH_SCHOOL: 2, // 高中/中专
  ASSOCIATE: 3,   // 大专
  BACHELOR: 4,    // 本科
  MASTER: 5       // 硕士
} as const

// 工作经验标准
export const EXPERIENCE = {
  UNLIMITED: 0,       // 不限
  FRESH_GRADUATE: 1,  // 应届毕业生
  UNDER_1_YEAR: 2,    // 1年以下
  YEARS_1_TO_3: 3,    // 1-3年
  YEARS_3_TO_5: 4,    // 3-5年
  YEARS_5_TO_10: 5,   // 5-10年
  OVER_10_YEARS: 6    // 10年以上
} as const



// 正职薪资标准 (按月，单位：元)
export const JOB_SALARY = {
  UNLIMITED: 0,      // 不限
  RANGE_3K_TO_6K: 1, // 3K-6K (3000-6000元)
  RANGE_6K_TO_10K: 2, // 6K-10K (6000-10000元)
  RANGE_10K_TO_15K: 3, // 10K-15K (10000-15000元)
  RANGE_15K_TO_25K: 4, // 15K-25K (15000-25000元)
  OVER_25K: 5        // 25K以上 (25000元以上)
} as const

// 公司规模标准 (使用数值编码)
export const COMPANY_SIZE = {
  UNLIMITED: 0,  // 不限
  STARTUP: 1,    // 1-20人
  SMALL: 2,      // 21-100人
  MEDIUM: 3,     // 101-500人
  LARGE: 4,      // 501-1000人
  ENTERPRISE: 5  // 1000人以上
} as const

// 福利待遇标准 (JSON数组，适合中低端岗位)
export const WELFARE_BENEFITS = {
  // 基础保障类
  SOCIAL_INSURANCE: 1,        // 五险一金
  INSURANCE: 2,               // 五险
  ACCOMMODATION: 3,           // 包食宿
  FOOD: 4,                   // 包吃
  HOUSING: 5,                // 包住
  MEAL_ALLOWANCE: 6,         // 餐补

  // 薪酬奖励类
  ANNUAL_BONUS: 7,           // 年终奖
  PERFORMANCE_BONUS: 8,      // 绩效奖金
  FULL_ATTENDANCE: 9,        // 全勤奖
  OVERTIME_PAY: 10,          // 加班费

  // 时间福利类
  PAID_LEAVE: 11,            // 带薪年假
  FLEXIBLE_WORK: 12,         // 弹性工作
  WEEKEND_OFF: 13,           // 双休
  LEGAL_HOLIDAYS: 14,        // 法定节假日

  // 交通住宿类
  TRANSPORT_ALLOWANCE: 15,   // 交通补贴
  DORMITORY: 16,             // 宿舍
  SHUTTLE_BUS: 17,           // 班车接送

  // 发展培训类
  TRAINING: 18,              // 培训机会
  PROMOTION_PATH: 19,        // 晋升通道
  SKILL_TRAINING: 20,        // 技能培训

  // 生活关怀类
  HOLIDAY_BENEFITS: 21,      // 节日福利
  BIRTHDAY_BENEFIT: 22,      // 生日福利
  TEAM_BUILDING: 23,         // 团建活动
  EMPLOYEE_DISCOUNT: 24,     // 员工优惠

  // 健康医疗类
  MEDICAL_EXAM: 25,          // 体检
  COMMERCIAL_INSURANCE: 26,  // 商业保险

} as const

// =====================================================
// 标准选项数据结构
// =====================================================

export interface StandardOption {
  code: number
  label: string
  value: string
  min?: number  // 最小值（用于范围类型）
  max?: number  // 最大值（用于范围类型）
}

export interface SalaryRange {
  code: number
  min: number  // 最小值（元）
  max: number  // 最大值（元）
}

export interface AgeRange {
  code: number
  min: number  // 最小年龄
  max: number  // 最大年龄
}

export interface CompanySizeRange {
  code: number
  min: number  // 最小人数
  max: number  // 最大人数
}

// =====================================================
// 预定义选项数据
// =====================================================

// 性别选项
export const genderOptions: StandardOption[] = [
  { code: GENDER.UNLIMITED, label: '不限', value: '不限' },
  { code: GENDER.MALE, label: '男', value: '男' },
  { code: GENDER.FEMALE, label: '女', value: '女' }
]

// 年龄范围选项
export const ageRangeOptions: StandardOption[] = [
  { code: AGE_RANGE.UNLIMITED, label: '不限', value: '不限' },
  { code: AGE_RANGE.AGE_18_TO_30, label: '18-30岁', value: '18-30岁' },
  { code: AGE_RANGE.AGE_31_TO_40, label: '31-40岁', value: '31-40岁' },
  { code: AGE_RANGE.AGE_41_TO_50, label: '41-50岁', value: '41-50岁' },
  { code: AGE_RANGE.AGE_50_PLUS, label: '50岁以上', value: '50岁以上' }
]

// 学历选项
export const educationOptions: StandardOption[] = [
  { code: EDUCATION.UNLIMITED, label: '不限', value: '不限' },
  { code: EDUCATION.JUNIOR_HIGH, label: '初中及以下', value: '初中及以下' },
  { code: EDUCATION.HIGH_SCHOOL, label: '高中/中专', value: '高中/中专' },
  { code: EDUCATION.ASSOCIATE, label: '大专', value: '大专' },
  { code: EDUCATION.BACHELOR, label: '本科', value: '本科' },
  { code: EDUCATION.MASTER, label: '硕士', value: '硕士' }
]

// 工作经验选项
export const experienceOptions: StandardOption[] = [
  { code: EXPERIENCE.UNLIMITED, label: '不限', value: '不限' },
  { code: EXPERIENCE.FRESH_GRADUATE, label: '应届毕业生', value: '应届毕业生' },
  { code: EXPERIENCE.UNDER_1_YEAR, label: '1年以下', value: '1年以下' },
  { code: EXPERIENCE.YEARS_1_TO_3, label: '1-3年', value: '1-3年' },
  { code: EXPERIENCE.YEARS_3_TO_5, label: '3-5年', value: '3-5年' },
  { code: EXPERIENCE.YEARS_5_TO_10, label: '5-10年', value: '5-10年' },
  { code: EXPERIENCE.OVER_10_YEARS, label: '10年以上', value: '10年以上' }
]

// 公司规模选项
export const companySizeOptions: StandardOption[] = [
  { code: COMPANY_SIZE.UNLIMITED, label: '不限', value: '不限' },
  { code: COMPANY_SIZE.STARTUP, label: '1-20人', value: '1-20人', min: 1, max: 20 },
  { code: COMPANY_SIZE.SMALL, label: '21-100人', value: '21-100人', min: 21, max: 100 },
  { code: COMPANY_SIZE.MEDIUM, label: '101-500人', value: '101-500人', min: 101, max: 500 },
  { code: COMPANY_SIZE.LARGE, label: '501-1000人', value: '501-1000人', min: 501, max: 1000 },
  { code: COMPANY_SIZE.ENTERPRISE, label: '1000人以上', value: '1000人以上', min: 1001, max: 999999 }
]

// 福利待遇选项（多选，JSON数组）
export const welfareOptions: StandardOption[] = [
  // 基础保障类
  { code: WELFARE_BENEFITS.SOCIAL_INSURANCE, label: '五险一金', value: '五险一金' },
  { code: WELFARE_BENEFITS.INSURANCE, label: '五险', value: '五险' },
  { code: WELFARE_BENEFITS.ACCOMMODATION, label: '包食宿', value: '包食宿' },
  { code: WELFARE_BENEFITS.FOOD, label: '包吃', value: '包吃' },
  { code: WELFARE_BENEFITS.HOUSING, label: '包住', value: '包住' },
  { code: WELFARE_BENEFITS.MEAL_ALLOWANCE, label: '餐补', value: '餐补' },

  // 薪酬奖励类
  { code: WELFARE_BENEFITS.ANNUAL_BONUS, label: '年终奖', value: '年终奖' },
  { code: WELFARE_BENEFITS.PERFORMANCE_BONUS, label: '绩效奖金', value: '绩效奖金' },
  { code: WELFARE_BENEFITS.FULL_ATTENDANCE, label: '全勤奖', value: '全勤奖' },
  { code: WELFARE_BENEFITS.OVERTIME_PAY, label: '加班费', value: '加班费' },

  // 时间福利类
  { code: WELFARE_BENEFITS.PAID_LEAVE, label: '带薪年假', value: '带薪年假' },
  { code: WELFARE_BENEFITS.FLEXIBLE_WORK, label: '弹性工作', value: '弹性工作' },
  { code: WELFARE_BENEFITS.WEEKEND_OFF, label: '双休', value: '双休' },
  { code: WELFARE_BENEFITS.LEGAL_HOLIDAYS, label: '法定节假日', value: '法定节假日' },

  // 交通住宿类
  { code: WELFARE_BENEFITS.TRANSPORT_ALLOWANCE, label: '交通补贴', value: '交通补贴' },
  { code: WELFARE_BENEFITS.DORMITORY, label: '宿舍', value: '宿舍' },
  { code: WELFARE_BENEFITS.SHUTTLE_BUS, label: '班车接送', value: '班车接送' },

  // 发展培训类
  { code: WELFARE_BENEFITS.TRAINING, label: '培训机会', value: '培训机会' },
  { code: WELFARE_BENEFITS.PROMOTION_PATH, label: '晋升通道', value: '晋升通道' },
  { code: WELFARE_BENEFITS.SKILL_TRAINING, label: '技能培训', value: '技能培训' },

  // 生活关怀类
  { code: WELFARE_BENEFITS.HOLIDAY_BENEFITS, label: '节日福利', value: '节日福利' },
  { code: WELFARE_BENEFITS.BIRTHDAY_BENEFIT, label: '生日福利', value: '生日福利' },
  { code: WELFARE_BENEFITS.TEAM_BUILDING, label: '团建活动', value: '团建活动' },
  { code: WELFARE_BENEFITS.EMPLOYEE_DISCOUNT, label: '员工优惠', value: '员工优惠' },

  // 健康医疗类
  { code: WELFARE_BENEFITS.MEDICAL_EXAM, label: '体检', value: '体检' },
  { code: WELFARE_BENEFITS.COMMERCIAL_INSURANCE, label: '商业保险', value: '商业保险' },
]

// 正职薪资范围映射 (单位：元/月)
export const jobSalaryRanges: Record<number, SalaryRange> = {
  [JOB_SALARY.UNLIMITED]: { code: JOB_SALARY.UNLIMITED, min: 0, max: 0 },
  [JOB_SALARY.RANGE_3K_TO_6K]: { code: JOB_SALARY.RANGE_3K_TO_6K, min: 3000, max: 6000 },
  [JOB_SALARY.RANGE_6K_TO_10K]: { code: JOB_SALARY.RANGE_6K_TO_10K, min: 6000, max: 10000 },
  [JOB_SALARY.RANGE_10K_TO_15K]: { code: JOB_SALARY.RANGE_10K_TO_15K, min: 10000, max: 15000 },
  [JOB_SALARY.RANGE_15K_TO_25K]: { code: JOB_SALARY.RANGE_15K_TO_25K, min: 15000, max: 25000 },
  [JOB_SALARY.OVER_25K]: { code: JOB_SALARY.OVER_25K, min: 25000, max: 99999999 }
}

// 年龄范围映射
export const ageRanges: Record<number, AgeRange> = {
  [AGE_RANGE.UNLIMITED]: { code: AGE_RANGE.UNLIMITED, min: 0, max: 0 },
  [AGE_RANGE.AGE_18_TO_30]: { code: AGE_RANGE.AGE_18_TO_30, min: 18, max: 30 },
  [AGE_RANGE.AGE_31_TO_40]: { code: AGE_RANGE.AGE_31_TO_40, min: 31, max: 40 },
  [AGE_RANGE.AGE_41_TO_50]: { code: AGE_RANGE.AGE_41_TO_50, min: 41, max: 50 },
  [AGE_RANGE.AGE_50_PLUS]: { code: AGE_RANGE.AGE_50_PLUS, min: 51, max: 100 }
}

// 公司规模范围映射
export const companySizeRanges: Record<number, CompanySizeRange> = {
  [COMPANY_SIZE.UNLIMITED]: { code: COMPANY_SIZE.UNLIMITED, min: 0, max: 0 },
  [COMPANY_SIZE.STARTUP]: { code: COMPANY_SIZE.STARTUP, min: 1, max: 20 },
  [COMPANY_SIZE.SMALL]: { code: COMPANY_SIZE.SMALL, min: 21, max: 100 },
  [COMPANY_SIZE.MEDIUM]: { code: COMPANY_SIZE.MEDIUM, min: 101, max: 500 },
  [COMPANY_SIZE.LARGE]: { code: COMPANY_SIZE.LARGE, min: 501, max: 1000 },
  [COMPANY_SIZE.ENTERPRISE]: { code: COMPANY_SIZE.ENTERPRISE, min: 1001, max: 999999 }
}

// =====================================================
// 工具函数
// =====================================================

export function getJobSalaryRange(code: number): SalaryRange | undefined {
  return jobSalaryRanges[code]
}

// 获取年龄范围
export function getAgeRange(code: number): AgeRange | undefined {
  return ageRanges[code]
}

// 获取公司规模范围
export function getCompanySizeRange(code: number): CompanySizeRange | undefined {
  return companySizeRanges[code]
}

// 根据编码数组获取福利标签
export function getWelfareLabels(codes: number[]): string[] {
  return codes.map(code => {
    const option = welfareOptions.find(opt => opt.code === code)
    return option ? option.label : ''
  }).filter(Boolean)
}

// 根据编码获取福利选项
export function getWelfareByCode(code: number): StandardOption | undefined {
  return welfareOptions.find(option => option.code === code)
}

// 检查福利编码数组是否包含指定福利
export function containsWelfare(codes: number[], targetCode: number): boolean {
  return codes.includes(targetCode)
} 