// @/src/constants/gig.ts

// ====================================================================
// 零工模块常量定义
// 与 backend/internal/constants/gig.go 保持严格一致
// ====================================================================

/**
 * 零工状态 (GigStatus)
 * @description 定义了零工从发布到完成的整个生命周期状态。
 * @dev 状态值遵循 'snake_case' 规范，前端枚举成员遵循 'PascalCase'。
 */
export enum GigStatus {
    /**
     * 草稿 (draft)
     * @description 已创建但未发布，仅发布者可见，可自由编辑。
     */
    Draft = 'draft',

    /**
     * 招募中 (recruiting)
     * @description 已公开发布，正在接受求职者报名。
     */
    Recruiting = 'recruiting',

    /**
     * 暂停中 (paused)
     * @description 发布者手动暂停招募，不再接受新申请，但可随时恢复为“招募中”。
     */
    Paused = 'paused',

    /**
     * 招募截止 (locked)
     * @description 明确表示招募已停止（因人数招满或报名截止），不再接受新申请，等待工作开始。
     */
    Locked = 'locked',

    /**
     * 进行中 (in_progress)
     * @description 零工已到达预定开始时间，工作正在进行中。
     */
    InProgress = 'in_progress',

    /**
     * 已完成 (completed)
     * @description 整个零工流程（工作、确认等）全部结束。这是一个最终的、成功的状态。
     */
    Completed = 'completed',

    /**
     * 已关闭 (closed)
     * @description 零工在“完成”之前被终止。这是一个最终的、非成功的状态。
     * @reason 1. 发布者手动关闭。 2. 报名到期后系统自动关闭。
     */
    Closed = 'closed',
}

/**
 * 零工申请状态 (GigApplicationStatus)
 * @description 定义了零工申请的完整流程状态。
 * @dev 状态值遵循 'snake_case' 规范。
 */
export enum GigApplicationStatus {
    Pending = 'pending',     // 待处理 (用户申请后)
    Withdrawn = 'withdrawn', // 已撤回 (用户在录用前撤销)
    Confirmed = 'confirmed', // 已录用 (发布者确认)
    Rejected = 'rejected',   // 未录用 (发布者拒绝)
    Cancelled = 'cancelled', // 已取消 (用户在录用后取消)
    Completed = 'completed', // 已完成 (工作完成)
    NoShow = 'no_show',      // 未到场 (用户录用但未到场)
}

/**
 * 零工审批模式 (GigApprovalMode)
 */
export enum GigApprovalMode {
    Manual = 'manual', // 手动审核
    Auto = 'auto',     // 自动录用
}


export interface SalaryRange {
    code: number
    min: number  // 最小值（分）
    max: number  // 最大值（分）
}

/**
 * 零工打卡方式 (GigCheckInMethod)
 */
export enum GigCheckInMethod {
    None = 'none',     // 无需打卡
    Gps = 'gps',       // GPS打卡
    QrCode = 'qrcode', // 二维码打卡
}


// 零工薪资标准 (简化版 - 按小时，单位：分)
export const GIG_SALARY = {
    UNLIMITED: 0,     // 不限
    RANGE_20_TO_40: 1, // 20-40元/小时 (2000-4000分)
    RANGE_40_TO_70: 2, // 40-70元/小时 (4000-7000分)
    RANGE_70_TO_100: 3, // 70-100元/小时 (7000-10000分)
    OVER_100: 4       // 100元以上/小时 (10000分以上)
} as const


// 零工薪资范围映射 (单位：元/小时)
export const gigSalaryRanges: Record<number, SalaryRange> = {
    [GIG_SALARY.UNLIMITED]: { code: GIG_SALARY.UNLIMITED, min: 0, max: 0 },
    [GIG_SALARY.RANGE_20_TO_40]: { code: GIG_SALARY.RANGE_20_TO_40, min: 20, max: 40 },
    [GIG_SALARY.RANGE_40_TO_70]: { code: GIG_SALARY.RANGE_40_TO_70, min: 40, max: 70 },
    [GIG_SALARY.RANGE_70_TO_100]: { code: GIG_SALARY.RANGE_70_TO_100, min: 70, max: 100 },
    [GIG_SALARY.OVER_100]: { code: GIG_SALARY.OVER_100, min: 100, max: 9999 }
}

// ====================================================================
// UI相关常量：文本、颜色等
// ====================================================================

/**
 * 零工状态对应的文本、颜色等UI详情
 */
export const GIG_STATUS_DETAILS: Record<GigStatus, { text: string; color: string; class: string }> = {
    [GigStatus.Draft]: { text: '草稿', color: '#909399', class: 'status-draft' },
    [GigStatus.Recruiting]: { text: '招募中', color: '#19be6b', class: 'status-recruiting' },
    [GigStatus.Paused]: { text: '暂停中', color: '#ff9900', class: 'status-paused' },
    [GigStatus.Locked]: { text: '招募截止', color: '#2979ff', class: 'status-locked' },
    [GigStatus.InProgress]: { text: '进行中', color: '#2979ff', class: 'status-ongoing' },
    [GigStatus.Completed]: { text: '已完成', color: '#909399', class: 'status-completed' },
    [GigStatus.Closed]: { text: '已关闭', color: '#fa3534', class: 'status-closed' },
};

/**
 * 零工申请状态对应的文本和颜色 (面向求职者)
 */
export const GIG_APPLICATION_STATUS_DETAILS_SEEKER: Record<GigApplicationStatus, { text: string; color: string; class: string }> = {
    [GigApplicationStatus.Pending]: { text: '待处理', color: '#ff9900', class: 'status-pending' },
    [GigApplicationStatus.Withdrawn]: { text: '已撤回', color: '#909399', class: 'status-withdrawn' },
    [GigApplicationStatus.Confirmed]: { text: '已录用', color: '#19be6b', class: 'status-confirmed' },
    [GigApplicationStatus.Rejected]: { text: '未录用', color: '#fa3534', class: 'status-rejected' },
    [GigApplicationStatus.Cancelled]: { text: '已取消', color: '#909399', class: 'status-cancelled' },
    [GigApplicationStatus.Completed]: { text: '已完成', color: '#909399', class: 'status-completed' },
    [GigApplicationStatus.NoShow]: { text: '未到场', color: '#fa3534', class: 'status-no-show' },
};

/**
 * 零工申请状态对应的文本和颜色 (面向招聘者)
 */
export const GIG_APPLICATION_STATUS_DETAILS_RECRUITER: Record<GigApplicationStatus, { text: string; color: string; class: string }> = {
    [GigApplicationStatus.Pending]: { text: '待审核', color: '#2979ff', class: 'status-pending' },
    [GigApplicationStatus.Withdrawn]: { text: '对方已撤回', color: '#909399', class: 'status-withdrawn' },
    [GigApplicationStatus.Confirmed]: { text: '已录用', color: '#19be6b', class: 'status-confirmed' },
    [GigApplicationStatus.Rejected]: { text: '已拒绝', color: '#fa3534', class: 'status-rejected' },
    [GigApplicationStatus.Cancelled]: { text: '对方已取消', color: '#909399', class: 'status-cancelled' },
    [GigApplicationStatus.Completed]: { text: '已完成', color: '#909399', class: 'status-completed' },
    [GigApplicationStatus.NoShow]: { text: '未到场', color: '#fa3534', class: 'status-no-show' },
};

// 其他业务枚举
export const GIG_SALARY_UNIT_MAP = { 1: '小时', 2: '天', 3: '件', 4: '总价' };
export const SETTLEMENT_METHODS = { 1: '日结', 2: '周结', 3: '月结', 4: '完工结' };

// 审核方式选项
export const GIG_APPROVAL_MODE_MAP = {
  'manual': '手动审核',
  'auto': '自动录用'
};

// 打卡方式选项
export const GIG_CHECKIN_METHOD_MAP = {
  'none': '无需打卡',
  'gps': 'GPS打卡',
  'qrcode': '二维码打卡'
};

// 性别、经验、学历常量已移至 @/constants/standards.ts 中的全局常量


// ====================================================================
// 反向映射 (用于数据提交)
// ====================================================================
function createReverseMap(map: Record<number, string>): Record<string, number> {
    const reverseMap: Record<string, number> = {};
    for (const key in map) {
        if (Object.prototype.hasOwnProperty.call(map, key)) {
            reverseMap[map[key]] = Number(key);
        }
    }
    return reverseMap;
}

export const REVERSE_GIG_SALARY_UNIT_MAP = createReverseMap(GIG_SALARY_UNIT_MAP);
export const REVERSE_SETTLEMENT_METHODS_MAP = createReverseMap(SETTLEMENT_METHODS);
// 性别、经验、学历的反向映射已移至 @/constants/standards.ts


// ====================================================================
// 表单选项 (用于 Picker 组件等)
// ====================================================================
function createOptions(map: Record<number, string>): { label: string; value: number }[] {
    return Object.entries(map).map(([value, label]) => ({
        label,
        value: Number(value),
    }));
}

export const GIG_SALARY_UNIT_OPTIONS = createOptions(GIG_SALARY_UNIT_MAP);
export const SETTLEMENT_METHOD_OPTIONS = createOptions(SETTLEMENT_METHODS);

// 审核方式选项 (用于前端picker)
export const GIG_APPROVAL_MODE_OPTIONS = Object.entries(GIG_APPROVAL_MODE_MAP).map(([value, label]) => ({
  label,
  value
}));

// 打卡方式选项 (用于前端picker)
export const GIG_CHECKIN_METHOD_OPTIONS = Object.entries(GIG_CHECKIN_METHOD_MAP).map(([value, label]) => ({
  label,
  value
}));

// 性别、经验、学历的选项已移至 @/constants/standards.ts


// ====================================================================
// 辅助函数
// ====================================================================

/**
 * 获取零工状态详情
 * @param status 状态值
 * @returns { text: string; color: string; class: string }
 */
export function getGigStatusDetails(status: GigStatus) {
    return GIG_STATUS_DETAILS[status] || GIG_STATUS_DETAILS[GigStatus.Draft];
}

/**
 * 获取申请状态详情
 * @param status 状态值
 * @param role 角色: 'seeker' 或 'recruiter'
 * @returns { text: string; color: string; class: string }
 */
export function getGigApplicationStatusDetails(status: GigApplicationStatus, role: 'seeker' | 'recruiter' = 'seeker') {
    const detailsMap = role === 'seeker' ? GIG_APPLICATION_STATUS_DETAILS_SEEKER : GIG_APPLICATION_STATUS_DETAILS_RECRUITER;
    return detailsMap[status] || GIG_APPLICATION_STATUS_DETAILS_SEEKER[GigApplicationStatus.Pending];
}
