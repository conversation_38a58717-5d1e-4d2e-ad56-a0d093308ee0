/**
 * 通用常量定义
 * 注意：标准化的选择器数据已迁移到 @/constants/standards.ts
 */

// 导入标准化定义
import {
  genderOptions,
  ageRangeOptions,
  educationOptions,
  experienceOptions,
  companySizeOptions,
  welfareOptions,
} from './standards'

// 兼容性导出 - 保持旧的导出名称以支持现有代码
export { genderOptions as GENDER_OPTIONS }
export { ageRangeOptions as AGE_RANGE_OPTIONS }
export { educationOptions as EDUCATION_OPTIONS }
export { experienceOptions as EXPERIENCE_OPTIONS }
export { companySizeOptions as COMPANY_SIZE_OPTIONS }
export { welfareOptions as WELFARE_OPTIONS }


// 原有的其他常量保持不变
export const jobCategories = [
  { code: 1, name: '普工/操作工', icon: 'icon-pugong' },
  { code: 2, name: '销售', icon: 'icon-xiaoshou' },
  { code: 3, name: '客服', icon: 'icon-kefu' },
  { code: 4, name: '司机', icon: 'icon-siji' },
  { code: 5, name: '餐饮', icon: 'icon-canyin' },
  { code: 6, name: '保安', icon: 'icon-baoan' },
  { code: 7, name: '保洁', icon: 'icon-baojie' },
  { code: 8, name: '技术工人', icon: 'icon-jishu' },
  { code: 9, name: '行政/文员', icon: 'icon-xingzheng' },
  { code: 10, name: '其他', icon: 'icon-qita' }
]

export const workTypes = [
  { code: 1, name: '全职', color: '#007AFF' },
  { code: 2, name: '兼职', color: '#34C759' },
  { code: 3, name: '实习', color: '#FF9500' },
  { code: 4, name: '临时', color: '#FF3B30' }
]

export const urgencyLevels = [
  { code: 1, name: '普通', color: '#8E8E93' },
  { code: 2, name: '紧急', color: '#FF9500' },
  { code: 3, name: '非常紧急', color: '#FF3B30' }
]

export const gigCategories = [
  { code: 1, name: '搬运装卸', icon: 'icon-banyun' },
  { code: 2, name: '清洁保洁', icon: 'icon-qingjie' },
  { code: 3, name: '派发传单', icon: 'icon-paifa' },
  { code: 4, name: '活动兼职', icon: 'icon-huodong' },
  { code: 5, name: '促销导购', icon: 'icon-cuxiao' },
  { code: 6, name: '数据录入', icon: 'icon-shuju' },
  { code: 7, name: '线上任务', icon: 'icon-xianshangrenwu' },
  { code: 8, name: '跑腿代办', icon: 'icon-paotui' },
  { code: 9, name: '技能服务', icon: 'icon-jineng' },
  { code: 10, name: '其他零工', icon: 'icon-qita' }
]

// 导出所有新的标准化常量
export * from './standards'

// 表单验证规则
export const FORM_RULES = {
  REQUIRED: 'required',
  NUMBER: 'number',
  EMAIL: 'email',
  PHONE: 'phone',
  ID_CARD: 'idCard'
};

// 图片上传相关常量
export const IMAGE_UPLOAD = {
  MAX_COUNT: 9,
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['jpg', 'jpeg', 'png', 'webp'],
  QUALITY: 80
};

// 房源状态
export const HOUSE_STATUS = [
  { label: "待审核", value: "pending" },
  { label: "已上架", value: "active" },
  { label: "已下架", value: "inactive" },
  { label: "已租出", value: "rented" },
  { label: "已售出", value: "sold" }
];

// 联系方式类型
export const CONTACT_TYPE = [
  { label: "手机", value: "mobile" },
  { label: "座机", value: "landline" },
  { label: "微信", value: "wechat" },
  { label: "QQ", value: "qq" }
];

// 健康证选项
export const HEALTH_CERTIFICATE_OPTIONS = [
  { label: "不需要", value: 0 },
  { label: "需要", value: 1 }
];

// 结算方式选项
export const SETTLEMENT_OPTIONS = [
  { label: "日结", value: "日结" },
  { label: "周结", value: "周结" },
  { label: "月结", value: "月结" },
  { label: "完工结", value: "完工结" }
];

// 缓存键名
export const CACHE_KEYS = {
  USER_INFO: 'USER_INFO',
  CITY_INFO: 'CITY_INFO',
  SEARCH_HISTORY: 'SEARCH_HISTORY'
}; 