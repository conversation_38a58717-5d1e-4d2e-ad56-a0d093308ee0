<script setup lang="ts">
import { onMounted } from "vue";
import { useGlobalStore } from "@/stores/global";
import { navigateTo } from "@/utils/navigation";

onMounted(() => {
  const globalStore = useGlobalStore();

  // 加载保存的角色状态
  globalStore.loadGigRole();

  // 根据角色直接跳转，无加载页面
  const targetPage =
    globalStore.gigRole === "recruiter"
      ? "/pages/gig/recruiter"
      : "/pages/gig/seeker";

  navigateTo({ url: targetPage, openType: "redirect" });
});
</script>

<template>
  <!-- 空模板，页面会立即跳转 -->
</template>
