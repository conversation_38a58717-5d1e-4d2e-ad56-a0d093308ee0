<template>
  <view class="gig-recruiter-page bg-page min-h-screen">
    <!-- 顶部头部 -->
    <view class="header-section bg-white">
      <view class="flex items-center justify-between px-30rpx py-20rpx">
        <view class="header-info">
          <text class="welcome-text text-base font-bold">招聘管理</text>
          <text class="subtitle text-secondary text-sm"
            >发布零工，找到合适的人才</text
          >
        </view>
        <!-- 切换到找零工模式按钮 -->
        <view
          class="mode-switch bg-success-light rounded-full px-24rpx py-12rpx"
          @tap="switchToSeeker"
        >
          <view class="flex items-center">
            <text class="i-carbon-user text-20rpx text-green mr-8rpx"></text>
            <text class="text-green font-semibold text-26rpx">找零工</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 快捷功能入口 -->
    <view class="function-entrance bg-white px-20rpx py-30rpx">
      <view class="grid grid-cols-3 gap-20rpx">
        <view
          v-for="func in recruiterFunctions"
          :key="func.id"
          class="function-item flex flex-col items-center justify-center py-20rpx"
          @tap="navigateToFunction(func.path)"
        >
          <view
            class="icon-wrapper w-88rpx h-88rpx rounded-2xl flex items-center justify-center mb-12rpx"
            :style="{
              background: func.gradient,
              boxShadow: `0 8rpx 24rpx ${func.shadowColor}`,
              transform: 'translateZ(0)',
            }"
          >
            <text
              :class="func.icon"
              class="text-44rpx text-white drop-shadow"
            ></text>
          </view>
          <text class="text-26rpx text-secondary font-medium">{{
            func.name
          }}</text>
        </view>
      </view>
    </view>

    <!-- 我的发布列表 -->
    <view class="published-section">
      <view class="section-header">
        <text class="section-title">我的发布</text>
        <view class="section-action" @tap="viewAllPublished">
          <text class="action-text">查看全部</text>
          <text class="i-carbon-chevron-right"></text>
        </view>
      </view>

      <view v-if="loadingGigs" class="loading-state">
        <view class="loading-skeleton">
          <view class="skeleton-card" v-for="i in 3" :key="i">
            <view class="skeleton-header">
              <view class="skeleton-title"></view>
              <view class="skeleton-status"></view>
            </view>
            <view class="skeleton-content">
              <view class="skeleton-line"></view>
              <view class="skeleton-line short"></view>
            </view>
          </view>
        </view>
      </view>

      <view v-else-if="errorGigs" class="error-state">
        <view class="error-message">{{ errorGigs }}</view>
        <view class="retry-button" @tap="fetchRecentPublished">重新加载</view>
      </view>

      <view v-else-if="recentPublished.length > 0" class="published-list">
        <view
          v-for="gig in recentPublished"
          :key="gig.id"
          class="gig-card-wrapper"
          @tap="navigateToDetail(gig.id)"
        >
          <GigCard :gig="gig" mode="recruiter" :show-status="true" />
        </view>
      </view>

      <view v-else class="empty-published">
        <view class="empty-content">
          <view class="empty-icon">
            <text class="i-carbon-document-add text-60rpx text-gray-400"></text>
          </view>
          <text class="empty-title">还没有发布任何零工</text>
          <text class="empty-desc">发布你的第一个零工，开始招聘吧</text>
          <view class="empty-action" @tap="goToPublish">
            <text class="action-text">立即发布</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 最新报名 -->
    <view class="applications-section">
      <view class="section-header">
        <text class="section-title">最新报名</text>
        <view class="section-action" @tap="viewAllApplications">
          <text class="action-text">查看全部</text>
          <text class="i-carbon-chevron-right"></text>
        </view>
      </view>

      <view v-if="loadingApps" class="loading-state">
        <view class="loading-applications">
          <view class="app-skeleton" v-for="i in 5" :key="i">
            <view class="skeleton-avatar"></view>
            <view class="skeleton-info">
              <view class="skeleton-name"></view>
              <view class="skeleton-job"></view>
            </view>
            <view class="skeleton-time"></view>
          </view>
        </view>
      </view>

      <view v-else-if="errorApps" class="error-state">
        <view class="error-message">{{ errorApps }}</view>
        <view class="retry-button" @tap="fetchRecentApplications"
          >重新加载</view
        >
      </view>

      <view v-else-if="recentApplications.length > 0" class="applications-list">
        <view
          v-for="app in recentApplications"
          :key="app.id"
          class="application-item"
          @tap="navigateToApplicants(app.gig_id)"
        >
          <view class="app-avatar">
            <image
              :src="app.applicant_info?.avatar || defaultAvatar"
              class="avatar"
            />
          </view>
          <view class="app-info">
            <text class="app-name">{{
              app.applicant_info?.nickname || "匿名用户"
            }}</text>
            <text class="app-job">报名了《{{ app.gig_info?.title }}》</text>
          </view>
          <view class="app-time">
            <text>{{ formatRelativeTime(app.created_at) }}</text>
          </view>
          <view class="app-actions">
            <view class="quick-action approve" @tap.stop="quickApprove(app)">
              <text class="action-icon i-carbon-checkmark"></text>
            </view>
            <view class="quick-action reject" @tap.stop="quickReject(app)">
              <text class="action-icon i-carbon-close"></text>
            </view>
          </view>
        </view>
      </view>

      <view v-else class="empty-applications">
        <view class="empty-content">
          <view class="empty-icon">
            <text
              class="i-carbon-user-multiple text-60rpx text-gray-400"
            ></text>
          </view>
          <text class="empty-title">暂无新的报名</text>
          <text class="empty-desc">有新的申请时会在这里显示</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onShow } from "@dcloudio/uni-app";
import GigCard from "./components/GigCard.vue";
import type { GigListItem, GigApplication } from "@/types/gig";
import { listGigs, listMyApplications } from "@/api/gig";
import defaultAvatar from "@/static/images/default-avatar.png";
import { useGlobalStore } from "@/stores/global";
import { formatRelativeTime } from "@/utils/date";
import { navigateTo } from "@/utils/navigation";
import { showActionConfirm, showSuccessToast, showErrorToast } from "@/utils/ui";
import { simpleApiCall } from "@/utils/api-helper";
import { buildUrl } from "@/utils/navigation";

// 招人角色的功能入口
const recruiterFunctions = [
  {
    id: "publish_gig",
    name: "发布零工",
    icon: "i-carbon-add-alt",
    path: "/pages/gig/publish",
    gradient: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
    shadowColor: "rgba(250, 112, 154, 0.3)",
  },
  {
    id: "my_posts",
    name: "我的发布",
    icon: "i-carbon-document-tasks",
    path: "/pages/gig/manage",
    gradient: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
    shadowColor: "rgba(168, 237, 234, 0.3)",
  },
  {
    id: "manage_applicants",
    name: "管理应聘",
    icon: "i-carbon-user-multiple",
    path: "/pages/gig/manage",
    gradient: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)",
    shadowColor: "rgba(255, 154, 158, 0.3)",
  },
];

const recentPublished = ref<GigListItem[]>([]);
const recentApplications = ref<GigApplication[]>([]);
const loadingGigs = ref(true);
const loadingApps = ref(true);
const errorGigs = ref<string | null>(null);
const errorApps = ref<string | null>(null);

onShow(async () => {
  await fetchRecentPublished();
  await fetchRecentApplications();
});

// 获取最近发布的零工
const fetchRecentPublished = async () => {
  loadingGigs.value = true;
  errorGigs.value = null;
  try {
    // 这里应该调用获取我的发布API，而不是所有零工
    // const res = await getMyGigs({ page: 1, size: 3 });
    // 临时使用listGigs，但需要过滤出当前用户的发布
    const res = await listGigs({ page: 1, page_size: 3, sort_by: "latest" });
    // Alova返回的数据在data属性中
    const responseData = (res as any).data || res;
    if (responseData.list) {
      recentPublished.value = responseData.list;
    } else {
      errorGigs.value = "获取数据失败";
    }
  } catch (e: any) {
    errorGigs.value = e.message || "加载失败，请重试";
  } finally {
    loadingGigs.value = false;
  }
};

// 获取最新报名
const fetchRecentApplications = async () => {
  loadingApps.value = true;
  errorApps.value = null;
  try {
    const res = await listMyApplications({ page: 1, page_size: 5 });
    // Alova返回的数据在data属性中
    const responseData = (res as any).data || res;
    if (responseData.list) {
      recentApplications.value = responseData.list;
    } else {
      errorApps.value = "获取报名信息失败";
    }
  } catch (e: any) {
    errorApps.value = e.message || "加载失败，请重试";
  } finally {
    loadingApps.value = false;
  }
};

// formatTime 函数已移除，使用 utils/date.ts 中的 formatRelativeTime

// 统一的导航函数
const navigateToDetail = (id: number) => {
  navigateTo(buildUrl('/pages/gig/detail', { id }));
};

const navigateToApplicants = (gigId: number) => {
  navigateTo(buildUrl('/pages/gig/applicants', { gigId }));
};

const viewAllPublished = () => {
  navigateTo('/pages/gig/manage');
};

const viewAllApplications = () => {
  navigateTo('/pages/gig/manage');
};

const goToPublish = () => {
  navigateTo('/pages/gig/publish');
};

const switchToSeeker = () => {
  const globalStore = useGlobalStore();
  globalStore.setGigRole("seeker");
  navigateTo({ url: "/pages/gig/seeker", openType: "redirect" });
};

const navigateToFunction = (path: string) => {
  navigateTo(path);
};

// 快速审批申请
const quickApprove = async (app: GigApplication) => {
  showActionConfirm(
    "通过申请",
    `${app.applicant_name}的申请`,
    async () => {
      const result = await simpleApiCall(
        async () => {
          // TODO: 调用审批API
          // return await reviewApplication({ application_id: app.id, new_status: 'Confirmed' });
          
          // 临时模拟API调用
          return new Promise(resolve => setTimeout(resolve, 1000));
        },
        "审批操作失败"
      );
      
      if (result !== null) {
        showSuccessToast("已通过申请");
        await fetchRecentApplications();
      }
    }
  );
};

// 快速拒绝申请
const quickReject = async (app: GigApplication) => {
  showActionConfirm(
    "拒绝申请",
    `${app.applicant_name}的申请`,
    async () => {
      const result = await simpleApiCall(
        async () => {
          // TODO: 调用审批API
          // return await reviewApplication({ application_id: app.id, new_status: 'Rejected' });
          
          // 临时模拟API调用
          return new Promise(resolve => setTimeout(resolve, 1000));
        },
        "审批操作失败"
      );
      
      if (result !== null) {
        showSuccessToast("已拒绝申请");
        await fetchRecentApplications();
      }
    }
  );
};
</script>

<style scoped lang="scss">
.gig-recruiter-page {
  .header-section {
    .subtitle {
      display: block;
      margin-top: 4rpx;
    }
  }

  .function-entrance {
    .function-item {
      cursor: pointer;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      .icon-wrapper {
        position: relative;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

        &:active {
          transform: translateY(2rpx) scale(0.95);
          box-shadow: 0 4rpx 12rpx var(--shadow-color) !important;
        }

        &::before {
          content: "";
          position: absolute;
          top: -4rpx;
          left: -4rpx;
          right: -4rpx;
          bottom: -4rpx;
          border-radius: inherit;
          background: inherit;
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: -1;
        }

        &:hover::before {
          opacity: 0.1;
        }
      }

      .drop-shadow {
        filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
      }
    }
  }

  .published-section,
  .applications-section {
    background-color: var(--bg-card);
    margin-top: var(--spacing-10);
    padding: var(--spacing-10) var(--spacing-16);
  }
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-10);
    .section-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
      color: var(--text-base);
    }
    .section-action {
      display: flex;
      align-items: center;
      color: var(--text-info);
      font-size: var(--font-size-sm);
    }
  }

  // 加载状态样式
  .loading-state {
    padding: var(--spacing-20) 0;
  }

  .loading-skeleton {
    .skeleton-card {
      background-color: var(--bg-card);
      border-radius: var(--radius-lg);
      padding: var(--spacing-16);
      margin-bottom: var(--spacing-12);
      border: 1rpx solid var(--border-color);

      .skeleton-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-12);

        .skeleton-title,
        .skeleton-status {
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: var(--radius-sm);
        }

        .skeleton-title {
          width: 200rpx;
          height: 32rpx;
        }

        .skeleton-status {
          width: 80rpx;
          height: 24rpx;
        }
      }

      .skeleton-content {
        .skeleton-line {
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: var(--radius-sm);
          height: 24rpx;
          margin-bottom: var(--spacing-8);

          &.short {
            width: 60%;
          }
        }
      }
    }
  }

  .loading-applications {
    .app-skeleton {
      display: flex;
      align-items: center;
      padding: var(--spacing-12) 0;
      border-bottom: 1rpx solid var(--border-color);

      .skeleton-avatar,
      .skeleton-info,
      .skeleton-time {
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: var(--radius-sm);
      }

      .skeleton-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-right: var(--spacing-12);
      }

      .skeleton-info {
        flex: 1;
        margin-right: var(--spacing-12);

        .skeleton-name,
        .skeleton-job {
          height: 24rpx;
          margin-bottom: var(--spacing-6);
          background: linear-gradient(
            90deg,
            #f0f0f0 25%,
            #e0e0e0 50%,
            #f0f0f0 75%
          );
          background-size: 200% 100%;
          animation: skeleton-loading 1.5s infinite;
          border-radius: var(--radius-sm);
        }

        .skeleton-name {
          width: 120rpx;
        }

        .skeleton-job {
          width: 200rpx;
        }
      }

      .skeleton-time {
        width: 80rpx;
        height: 24rpx;
      }
    }
  }

  // 错误状态样式
  .error-state {
    text-align: center;
    padding: var(--spacing-40) 0;

    .error-message {
      color: var(--text-secondary);
      font-size: var(--font-size-base);
      margin-bottom: var(--spacing-16);
    }

    .retry-button {
      display: inline-block;
      padding: var(--spacing-8) var(--spacing-16);
      background-color: var(--primary);
      color: var(--text-inverse);
      border-radius: var(--radius-base);
      font-size: var(--font-size-sm);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }
    }
  }

  // 空状态样式
  .empty-content {
    text-align: center;
    padding: var(--spacing-40) 0;

    .empty-icon {
      margin-bottom: var(--spacing-16);
    }

    .empty-title {
      display: block;
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-bold);
      color: var(--text-base);
      margin-bottom: var(--spacing-8);
    }

    .empty-desc {
      display: block;
      font-size: var(--font-size-base);
      color: var(--text-secondary);
      margin-bottom: var(--spacing-20);
    }

    .empty-action {
      display: inline-block;
      padding: var(--spacing-10) var(--spacing-20);
      background-color: var(--primary);
      color: var(--text-inverse);
      border-radius: var(--radius-base);
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .application-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-12) 0;
    border-bottom: 1rpx solid var(--border-color);
    position: relative;

    &:last-child {
      border-bottom: none;
    }

    .app-avatar .avatar {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }

    .app-info {
      flex: 1;
      margin: 0 var(--spacing-12);

      .app-name {
        font-weight: var(--font-weight-bold);
        color: var(--text-base);
      }

      .app-job {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
        display: block;
        margin-top: 4rpx;
      }
    }

    .app-time {
      font-size: var(--font-size-xs);
      color: var(--text-info);
      margin-right: var(--spacing-12);
    }

    .app-actions {
      display: flex;
      gap: var(--spacing-8);

      .quick-action {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        .action-icon {
          font-size: 24rpx;
          color: var(--text-inverse);
        }

        &.approve {
          background-color: var(--text-green);

          &:active {
            transform: scale(0.9);
            background-color: var(--text-green);
          }
        }

        &.reject {
          background-color: var(--text-red);

          &:active {
            transform: scale(0.9);
            background-color: var(--text-red);
          }
        }
      }
    }
  }

  @keyframes skeleton-loading {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
}
</style>
