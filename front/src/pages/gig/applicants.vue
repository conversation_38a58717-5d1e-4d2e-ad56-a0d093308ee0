<template>
  <view class="applicants-page">
    <CustomNavBar title="报名管理" />

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载申请信息...</text>
    </view>

    <!-- 内容区域 -->
    <view v-else class="content-container">
      <!-- 申请者列表 -->
      <view v-if="applicants.length > 0" class="applicants-list">
        <view
          v-for="applicant in applicants"
          :key="applicant.id"
          class="applicant-card"
          @tap="goToApplicantProfile(applicant.user_id)"
        >
          <!-- 申请人头部信息 -->
          <view class="applicant-header">
            <view class="applicant-info">
              <image
                :src="applicant.applicant_info?.avatar || defaultAvatar"
                class="applicant-avatar"
              />
              <view class="applicant-details">
                <text class="applicant-name">{{
                  applicant.applicant_info?.nickname || "匿名用户"
                }}</text>
                <text class="apply-time">{{ formatDateSimple(applicant.created_at) }}</text>
              </view>
            </view>

            <view class="status-badge" :class="getStatusDetails(applicant.status).class">
              <text class="status-text">{{ getStatusDetails(applicant.status).text }}</text>
            </view>
          </view>

          <!-- 联系信息 -->
          <view class="contact-info">
            <view class="contact-item">
              <text class="contact-icon i-carbon-phone"></text>
              <text class="contact-text">{{ applicant.applicant_phone }}</text>
            </view>
          </view>

          <!-- 申请留言 -->
          <view v-if="applicant.message" class="application-message">
            <text class="message-label">申请留言：</text>
            <text class="message-content">{{ applicant.message }}</text>
          </view>

          <!-- 操作按钮区域 -->
          <view class="action-buttons">
            <template v-if="applicant.status === GigApplicationStatus.Pending">
              <button
                class="action-btn reject-btn"
                @click.stop="handleUpdateStatus(applicant.id, GigApplicationStatus.Rejected)"
              >
                <text class="btn-text">拒绝</text>
              </button>
              <button
                class="action-btn approve-btn"
                @click.stop="handleUpdateStatus(applicant.id, GigApplicationStatus.Confirmed)"
              >
                <text class="btn-text">录用</text>
              </button>
            </template>
            <template v-else>
              <button
                class="action-btn contact-btn"
                @click.stop="contactApplicant(applicant)"
              >
                <text class="btn-text">联系Ta</text>
              </button>
            </template>
          </view>
        </view>
      </view>

      <!-- 空状态页面 -->
      <view v-else class="empty-state">
        <view class="empty-illustration">
          <text class="empty-icon i-carbon-user-multiple"></text>
        </view>
        <view class="empty-content">
          <text class="empty-title">暂无报名信息</text>
          <text class="empty-desc">还没有人报名这个工作，请耐心等待或分享给更多人</text>
        </view>
        <view class="empty-actions">
          <button class="share-btn" @tap="handleShare">
            <text class="share-icon i-carbon-share"></text>
            <text class="share-text">分享工作</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { listGigApplications, reviewApplication } from "@/api/gig";
import type { GigApplication } from "@/types/gig";
import {
  GigApplicationStatus,
  getGigApplicationStatusDetails,
} from "@/constants/gig";
import { formatDateSimple } from "@/utils/date";
import { navigateTo, buildUrl } from "@/utils/navigation";
import { fetchList, simpleApiCall } from "@/utils/api-helper";
import { showSuccessToast, showErrorToast } from "@/utils/ui";
import defaultAvatar from "@/static/images/default-avatar.png";

const gigId = ref<number | null>(null);
const applicants = ref<GigApplication[]>([]);
const loading = ref(true);

onLoad(async (options) => {
  if (options && options.gigId) {
    gigId.value = parseInt(options.gigId, 10);
    await fetchApplicants();
  } else {
    loading.value = false;
    uni.showToast({ title: "无效的零工ID", icon: "none" });
  }
});

const fetchApplicants = async () => {
  if (!gigId.value) return;
  const result = await fetchList(async () => {
    const res = await listGigApplications(gigId.value, {
      page: 1,
      page_size: 100,
    });
    if (res.code === 0) {
      return res.data.list;
    }
    throw new Error(res.message || "获取申请列表失败");
  }, "暂无申请信息");

  applicants.value = result;
  loading.value = false;
};

const getStatusDetails = (status: GigApplicationStatus) => {
  return getGigApplicationStatusDetails(status, "recruiter");
};

const handleUpdateStatus = async (
  applicationId: number,
  newStatus: GigApplicationStatus
) => {
  const success = await simpleApiCall(async () => {
    const { code, message, data } = await reviewApplication({
      application_id: applicationId,
      new_status: newStatus,
    });

    if (code === 0) {
      // 找到对应的申请并更新其状态，避免重新请求整个列表
      const index = applicants.value.findIndex((a) => a.id === applicationId);
      if (index !== -1) {
        applicants.value[index].status = newStatus;
      } else {
        // 如果找不到，则刷新整个列表
        await fetchApplicants();
      }
      return data;
    } else {
      throw new Error(message || "操作失败");
    }
  }, "申请处理失败");

  if (success) {
    showSuccessToast("操作成功");
  }
};

const goToApplicantProfile = (userId?: number) => {
  if (!userId) return;
  // navigateTo(buildUrl('/pages/user/profile', { id: userId }));
  console.log(`跳转到用户详情页: ${userId}`);
};

const contactApplicant = (applicant: GigApplication) => {
  if (!applicant.applicant_phone) {
    uni.showToast({ title: "暂无联系方式", icon: "none" });
    return;
  }

  uni.makePhoneCall({
    phoneNumber: applicant.applicant_phone,
    success: () => {
      console.log("拨打电话成功");
    },
    fail: (err) => {
      console.log("拨打电话失败", err);
      showErrorToast("拨号功能不可用");
    },
  });
};

// 分享工作
const handleShare = () => {
  if (!gigId.value) return;

  // 简化分享逻辑，直接复制链接
  const shareUrl = `pages/gig/detail?id=${gigId.value}`;

  uni.setClipboardData({
    data: shareUrl,
    success: () => {
      uni.showToast({ title: "链接已复制到剪贴板", icon: "success" });
    },
    fail: () => {
      uni.showToast({ title: "复制失败", icon: "none" });
    }
  });
};
</script>

<style lang="scss" scoped>
.applicants-page {
  min-height: 100vh;
  background: var(--bg-page);
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 80rpx 40rpx;

  .loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid var(--bg-tag);
    border-top: 6rpx solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 32rpx;
  }

  .loading-text {
    font-size: 30rpx;
    color: var(--text-secondary);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 内容容器
.content-container {
  padding: 24rpx;
}

// 申请者列表
.applicants-list {
  .applicant-card {
    background: var(--bg-card);
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    padding: 32rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    }

    // 申请人头部
    .applicant-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;

      .applicant-info {
        display: flex;
        align-items: center;
        flex: 1;

        .applicant-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          background: var(--bg-tag);
        }

        .applicant-details {
          flex: 1;

          .applicant-name {
            font-size: 32rpx;
            font-weight: 600;
            color: var(--text-base);
            margin-bottom: 4rpx;
            display: block;
          }

          .apply-time {
            font-size: 24rpx;
            color: var(--text-info);
          }
        }
      }

      .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 600;

        .status-text {
          color: inherit;
        }

        &.status-pending {
          background: var(--bg-warning-light);
          color: var(--text-yellow);
        }

        &.status-confirmed {
          background: var(--bg-success-light);
          color: var(--text-green);
        }

        &.status-rejected {
          background: var(--bg-danger-light);
          color: var(--text-red);
        }

        &.status-withdrawn {
          background: var(--bg-tag);
          color: var(--text-info);
        }
      }
    }

    // 联系信息
    .contact-info {
      margin-bottom: 24rpx;

      .contact-item {
        display: flex;
        align-items: center;
        gap: 12rpx;

        .contact-icon {
          font-size: 28rpx;
          color: var(--primary);
        }

        .contact-text {
          font-size: 28rpx;
          color: var(--text-base);
          font-weight: 500;
        }
      }
    }

    // 申请留言
    .application-message {
      background: var(--bg-tag);
      border-radius: 16rpx;
      padding: 20rpx;
      margin-bottom: 24rpx;

      .message-label {
        font-size: 24rpx;
        color: var(--text-info);
        margin-bottom: 8rpx;
        display: block;
      }

      .message-content {
        font-size: 28rpx;
        color: var(--text-base);
        line-height: 1.5;
      }
    }

    // 操作按钮
    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 16rpx;

      .action-btn {
        border: none;
        border-radius: 32rpx;
        padding: 16rpx 32rpx;
        font-size: 26rpx;
        font-weight: 600;
        transition: all 0.3s ease;

        .btn-text {
          color: inherit;
        }

        &.reject-btn {
          background: var(--bg-danger-light);
          color: var(--text-red);

          &:active {
            background: var(--text-red);

            .btn-text {
              color: white;
            }
          }
        }

        &.approve-btn {
          background: linear-gradient(135deg, var(--text-green), #4ade80);
          color: white;
          box-shadow: 0 4rpx 16rpx rgba(34, 197, 94, 0.3);

          &:active {
            transform: scale(0.98);
            box-shadow: 0 2rpx 8rpx rgba(34, 197, 94, 0.4);
          }
        }

        &.contact-btn {
          background: var(--bg-primary-light);
          color: var(--primary);

          &:active {
            background: var(--primary);

            .btn-text {
              color: white;
            }
          }
        }
      }
    }
  }
}

// 空状态页面
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 80rpx 40rpx;
  text-align: center;

  .empty-illustration {
    margin-bottom: 40rpx;

    .empty-icon {
      font-size: 160rpx;
      color: var(--text-info);
      opacity: 0.6;
    }
  }

  .empty-content {
    margin-bottom: 48rpx;

    .empty-title {
      font-size: 36rpx;
      font-weight: 600;
      color: var(--text-base);
      margin-bottom: 16rpx;
      display: block;
    }

    .empty-desc {
      font-size: 28rpx;
      color: var(--text-secondary);
      line-height: 1.5;
      max-width: 500rpx;
    }
  }

  .empty-actions {
    .share-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      background: linear-gradient(135deg, var(--primary), #ff8533);
      color: white;
      border: none;
      border-radius: 44rpx;
      padding: 20rpx 40rpx;
      font-size: 28rpx;
      font-weight: 600;
      box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.4);
      }

      .share-icon {
        font-size: 32rpx;
        color: white;
      }

      .share-text {
        color: white;
      }
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .applicants-list .applicant-card .action-buttons {
    flex-direction: column;
    align-items: stretch;

    .action-btn {
      width: 100%;
      text-align: center;
    }
  }
}
</style>
