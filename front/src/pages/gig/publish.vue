<template>
  <view class="container">
    <view class="content">
      <tui-form ref="formRef" :model="formData" :rules="formRules">
        <!-- 基本信息 -->
        <view class="card">
          <view class="section-title">
            <text>基本信息</text>
          </view>

          <form-input
            prop="title"
            label="工作标题"
            :asterisk="true"
            v-model="formData.title"
            placeholder="请输入工作标题"
          />

          <form-input
            prop="start_time"
            label="开始时间"
            :asterisk="true"
            :arrow="true"
            @click="showStartTimePicker"
          >
            <view
              class="picker-input"
              :class="{ placeholder: !formData.start_time }"
            >
              {{ formData.start_time || "请选择开始时间" }}
            </view>
          </form-input>

          <form-input
            prop="end_time"
            label="结束时间"
            :asterisk="true"
            :arrow="true"
            @click="showEndTimePicker"
          >
            <view
              class="picker-input"
              :class="{ placeholder: !formData.end_time }"
            >
              {{ formData.end_time || "请选择结束时间" }}
            </view>
          </form-input>
          <form-input
            prop="address"
            label="工作地点"
            :asterisk="true"
            v-model="formData.address"
            placeholder="点击选择地点"
            :arrow="true"
            @tap="selectLocation"
            :disabled="true"
          />
          <form-input
            prop="detail_address"
            label="详细地址"
            :asterisk="true"
            v-model="formData.detail_address"
            placeholder="请输入楼号、楼层、门牌号等"
          />
          <form-input
            prop="people_count"
            label="招聘人数"
            :asterisk="true"
            v-model.number="formData.people_count"
            placeholder="请输入招聘人数"
            type="number"
            unit="人"
          />
          <!-- 合并薪酬和单位 -->
          <form-input prop="salary" label="工作薪酬" :asterisk="true">
            <view class="salary-input-group">
              <input
                v-model.number="formData.salary"
                class="salary-input"
                type="number"
                placeholder="请输入金额"
              />
              <text class="salary-unit">元/</text>
              <picker
                class="salary-picker"
                :value="salaryUnitPickerValue"
                :range="salaryUnitPickerOptions"
                @change="onSalaryUnitChange"
              >
                <view class="picker-input">
                  {{ selectedSalaryUnitLabel }}
                  <text class="picker-arrow">▼</text>
                </view>
              </picker>
            </view>
          </form-input>

          <form-input
            prop="settlement"
            label="结算方式"
            :asterisk="true"
            :arrow="true"
            :bottomBorder="false"
          >
            <picker
              :value="settlementPickerValue"
              :range="settlementPickerOptions"
              @change="onSettlementChange"
            >
              <view
                class="picker-input"
                :class="{ placeholder: !selectedSettlementLabel }"
                >{{ selectedSettlementLabel || "请选择结算方式" }}</view
              >
            </picker>
          </form-input>
        </view>

        <!-- 招聘要求 -->
        <view class="card">
          <view class="section-title">招聘要求</view>
          <!-- 性别年龄要求 -->
          <form-input
            prop="gender_age"
            label="性别年龄"
            :arrow="true"
            :asterisk="true"
            :bottomBorder="false"
          >
            <view class="gender-age-container" @click="showGenderAgeModal">
              <text
                class="gender-age-text"
                :class="{ placeholder: !genderAgeDisplayText }"
              >
                {{ genderAgeDisplayText || "请选择性别年龄要求" }}
              </text>
            </view>
          </form-input>

          <form-input prop="education" label="学历要求" :arrow="true">
            <picker
              :value="educationPickerValue"
              :range="educationPickerOptions"
              @change="onEducationChange"
            >
              <view
                class="picker-input"
                :class="{ placeholder: !formData.education }"
                >{{ selectedEducationLabel }}</view
              >
            </picker>
          </form-input>
          <form-input prop="experience" label="工作经验" :arrow="true">
            <picker
              :value="experiencePickerValue"
              :range="experiencePickerOptions"
              @change="onExperienceChange"
            >
              <view
                class="picker-input"
                :class="{ placeholder: !formData.experience }"
              >
                {{ selectedExperienceLabel }}
              </view>
            </picker>
          </form-input>

          <!-- 审核方式 -->
          <form-input
            prop="approval_mode"
            label="审核方式"
            :asterisk="true"
            :arrow="true"
          >
            <picker
              :value="approvalModePickerValue"
              :range="approvalModePickerOptions"
              @change="onApprovalModeChange"
            >
              <view
                class="picker-input"
                :class="{ placeholder: !selectedApprovalModeLabel }"
              >
                {{ selectedApprovalModeLabel || "请选择审核方式" }}
              </view>
            </picker>
          </form-input>

          <!-- 审核方式说明 -->
          <view v-if="formData.approval_mode" class="option-description">
            <text class="description-text">{{ approvalModeDescription }}</text>
          </view>

          <!-- 打卡方式 -->
          <form-input
            prop="check_in_method"
            label="打卡方式"
            :asterisk="true"
            :arrow="true"
          >
            <picker
              :value="checkInMethodPickerValue"
              :range="checkInMethodPickerOptions"
              @change="onCheckInMethodChange"
            >
              <view
                class="picker-input"
                :class="{ placeholder: !selectedCheckInMethodLabel }"
              >
                {{ selectedCheckInMethodLabel || "请选择打卡方式" }}
              </view>
            </picker>
          </form-input>

          <!-- 打卡方式说明 -->
          <view
            v-if="formData.check_in_method"
            class="option-description"
            :bottomBorder="false"
          >
            <text class="description-text">{{ checkInMethodDescription }}</text>
          </view>
        </view>

        <!-- 工作描述 -->
        <view class="card description-card">
          <view class="description-section">
            <view class="description-label">
              <text>工作描述</text>
              <text class="char-count"
                >{{ formData.description.length }}/500</text
              >
            </view>
            <textarea
              v-model="formData.description"
              placeholder="请详细描述工作内容、要求等信息"
              class="description-textarea"
              maxlength="500"
              placeholder-class="textarea-placeholder"
            />
          </view>
        </view>

        <!-- 联系方式 -->
        <view class="card">
          <view class="section-title">
            <text>联系方式</text>
          </view>

          <form-input
            prop="contact_name"
            label="联系人"
            :asterisk="true"
            v-model="formData.contact_name"
            placeholder="请输入联系人姓名"
          />

          <form-input
            prop="contact_phone"
            label="联系电话"
            :asterisk="true"
            v-model="formData.contact_phone"
            placeholder="请输入联系电话"
          />
        </view>
      </tui-form>

      <!-- 协议确认 -->
      <view class="card">
        <view class="agreement-item" @click="toggleAgreement">
          <view class="checkbox" :class="{ checked: agreedToTerms }">
            <text v-if="agreedToTerms">✓</text>
          </view>
          <view class="agreement-text">
            我已阅读并同意《用户服务协议》和《隐私政策》
          </view>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="submit-section">
        <tui-button
          @click="handleSubmit"
          :disabled="!canSubmit"
          width="100%"
          height="96rpx"
          size="large"
          type="primary"
          shape="circle"
        >
          付费发布 3 元
        </tui-button>
      </view>
    </view>

    <!-- 时间选择器 -->
    <tui-datetime
      ref="startTimePicker"
      :type="1"
      title="选择开始时间"
      :set-date-time="formData.start_time"
      :minutes-data="minutesData"
      @confirm="onStartTimeConfirm"
    />

    <tui-datetime
      ref="endTimePicker"
      :type="1"
      title="选择结束时间"
      :minutes-data="minutesData"
      :set-date-time="formData.end_time"
      @confirm="onEndTimeConfirm"
    />

    <!-- 性别年龄选择弹窗 -->
    <GenderAgeSelector
      :show="showGenderAgeSelector"
      :gender="formData.gender"
      :min-age="formData.age_min"
      :max-age="formData.age_max"
      @close="closeGenderAgeModal"
      @confirm="onGenderAgeConfirm"
    />
  </view>
</template>

<script setup lang="ts">
import tuiForm from "@/components/thorui/tui-form/tui-form.vue";
import FormInput from "@/components/common/FormInput.vue";
import GenderAgeSelector from "@/components/gig/GenderAgeSelector.vue";
import { createGig } from "@/api/gig";
import type { CreateGigRequest } from "@/types/gig";
import {
  GIG_SALARY_UNIT_OPTIONS,
  SETTLEMENT_METHOD_OPTIONS,
  GIG_APPROVAL_MODE_OPTIONS,
  GIG_CHECKIN_METHOD_OPTIONS,
} from "@/constants/gig";
import {
  genderOptions,
  educationOptions,
  experienceOptions,
} from "@/constants/standards";
import { submitForm } from "@/utils/api-helper";
import { showToast } from "@/utils/ui";
import { navigateBack } from "@/utils/navigation";

const formRef = ref();
const agreedToTerms = ref(false);
const startTimePicker = ref();
const endTimePicker = ref();
const showGenderAgeSelector = ref(false);
const minutesData = ["00", "15", "30", "45"];

// --- Picker相关状态 ---
// 1. Picker的选项 (供给picker的range属性)
const salaryUnitPickerOptions =
  GIG_SALARY_UNIT_OPTIONS?.map((o) => o.label) || [];
const settlementPickerOptions =
  SETTLEMENT_METHOD_OPTIONS?.map((o) => o.label) || [];
const educationPickerOptions = educationOptions?.map((o) => o.label) || [];
const experiencePickerOptions = experienceOptions?.map((o) => o.label) || [];
const approvalModePickerOptions =
  GIG_APPROVAL_MODE_OPTIONS?.map((o) => o.label) || [];
const checkInMethodPickerOptions =
  GIG_CHECKIN_METHOD_OPTIONS?.map((o) => o.label) || [];

// 2. Picker当前选中项的索引 (用于双向绑定picker的value)
const salaryUnitPickerValue = ref(1); // 默认'天'
const settlementPickerValue = ref(0);
const educationPickerValue = ref(0);
const experiencePickerValue = ref(0);
const approvalModePickerValue = ref(0); // 默认手动审核
const checkInMethodPickerValue = ref(0); // 默认无需打卡

// 3. Picker选中后显示的文本 (计算属性，让template更干净)
const selectedSalaryUnitLabel = computed(
  () => GIG_SALARY_UNIT_OPTIONS?.[salaryUnitPickerValue.value]?.label || ""
);
const selectedSettlementLabel = computed(
  () => SETTLEMENT_METHOD_OPTIONS?.[settlementPickerValue.value]?.label || ""
);
const selectedEducationLabel = computed(
  () => educationOptions?.[educationPickerValue.value]?.label || ""
);
const selectedExperienceLabel = computed(
  () => experienceOptions?.[experiencePickerValue.value]?.label || ""
);
const selectedApprovalModeLabel = computed(
  () => GIG_APPROVAL_MODE_OPTIONS?.[approvalModePickerValue.value]?.label || ""
);
const selectedCheckInMethodLabel = computed(
  () =>
    GIG_CHECKIN_METHOD_OPTIONS?.[checkInMethodPickerValue.value]?.label || ""
);

// 性别年龄显示文本
const genderAgeDisplayText = computed(() => {
  const genderText =
    genderOptions?.find((g) => g.code === formData.gender)?.label || "不限";
  const ageText =
    formData.age_min === 16 && formData.age_max === 65
      ? "不限年龄"
      : `${formData.age_min}-${formData.age_max}岁`;
  return `${genderText}，${ageText}`;
});

// 审核方式说明
const approvalModeDescription = computed(() => {
  switch (formData.approval_mode) {
    case "manual":
      return "💭 手动审核：您需要逐一审核求职者的申请，可以查看简历和申请信息后决定是否录用";
    case "auto":
      return "⚡ 自动录用：系统将自动通过所有申请，求职者申请后即可参与工作，适合紧急招聘";
    default:
      return "";
  }
});

// 打卡方式说明
const checkInMethodDescription = computed(() => {
  switch (formData.check_in_method) {
    case "none":
      return "🚫 无需打卡：不进行考勤管理，适合灵活性较高的工作";
    case "gps":
      return "📍 GPS打卡：求职者需要在工作地点附近签到，系统会验证位置信息";
    case "qrcode":
      return "📱 二维码打卡：您提供二维码，求职者扫码签到，适合固定工作场所";
    default:
      return "";
  }
});

// --- 表单数据 ---
const initialFormData: CreateGigRequest = {
  title: "",
  start_time: "",
  end_time: "",
  address_name: "",
  address: "",
  detail_address: "",
  latitude: 0,
  longitude: 0,
  people_count: null,
  salary: null,
  salary_unit:
    GIG_SALARY_UNIT_OPTIONS?.[salaryUnitPickerValue.value]?.value || 1,
  settlement:
    SETTLEMENT_METHOD_OPTIONS?.[settlementPickerValue.value]?.value || 1,
  // gender: genderOptions?.[genderPickerValue.value]?.code || 0,
  gender: 0,
  age_min: 16, // 默认最小年龄
  age_max: 65, // 默认最大年龄
  education: educationOptions?.[educationPickerValue.value]?.code || 0,
  experience: experienceOptions?.[experiencePickerValue.value]?.code || 0,
  approval_mode:
    GIG_APPROVAL_MODE_OPTIONS?.[approvalModePickerValue.value]?.value ||
    "manual",
  check_in_method:
    GIG_CHECKIN_METHOD_OPTIONS?.[checkInMethodPickerValue.value]?.value ||
    "none",
  description: "",
  contact_name: "",
  contact_phone: "",
};
const formData = reactive<CreateGigRequest>({ ...initialFormData });

// --- 表单验证规则 ---
const formRules = [
  { name: "title", rule: ["required"], msg: "请输入工作标题" },
  { name: "start_time", rule: ["required"], msg: "请选择开始时间" },
  { name: "end_time", rule: ["required"], msg: "请选择结束时间" },
  { name: "address", rule: ["required"], msg: "请选择工作地点" },
  { name: "detail_address", rule: ["required"], msg: "请输入详细地址" },
  { name: "people_count", rule: ["required"], msg: "请输入招聘人数" },
  { name: "salary", rule: ["required"], msg: "请输入薪酬金额" },
  { name: "settlement", rule: ["required"], msg: "请选择结算方式" },
  { name: "approval_mode", rule: ["required"], msg: "请选择审核方式" },
  { name: "check_in_method", rule: ["required"], msg: "请选择打卡方式" },
  { name: "contact_name", rule: ["required"], msg: "请输入联系人" },
  { name: "contact_phone", rule: ["required", "isMobile"], msg: ["请输入联系电话", "请输入正确的联系电话格式"] },
];

// --- Picker事件处理 ---
const onSalaryUnitChange = (e: any) => {
  const index = Number(e.detail.value);
  salaryUnitPickerValue.value = index;
  formData.salary_unit = GIG_SALARY_UNIT_OPTIONS?.[index]?.value || 1;
};
const onSettlementChange = (e: any) => {
  const index = Number(e.detail.value);
  settlementPickerValue.value = index;
  formData.settlement = SETTLEMENT_METHOD_OPTIONS?.[index]?.value || 1;
};
const onEducationChange = (e: any) => {
  const index = Number(e.detail.value);
  educationPickerValue.value = index;
  formData.education = educationOptions?.[index]?.code || 0;
};
const onExperienceChange = (e: any) => {
  const index = Number(e.detail.value);
  experiencePickerValue.value = index;
  formData.experience = experienceOptions?.[index]?.code || 0;
};
const onApprovalModeChange = (e: any) => {
  const index = Number(e.detail.value);
  approvalModePickerValue.value = index;
  formData.approval_mode =
    GIG_APPROVAL_MODE_OPTIONS?.[index]?.value || "manual";
};
const onCheckInMethodChange = (e: any) => {
  const index = Number(e.detail.value);
  checkInMethodPickerValue.value = index;
  formData.check_in_method =
    GIG_CHECKIN_METHOD_OPTIONS?.[index]?.value || "none";
};

// --- 时间选择器 ---
const showStartTimePicker = () => startTimePicker.value?.show();
const showEndTimePicker = () => endTimePicker.value?.show();

const onStartTimeConfirm = (e: { result: string }) => {
  const dateTime = e.result;
  if (formData.end_time && new Date(dateTime) >= new Date(formData.end_time)) {
    uni.showToast({ title: "开始时间不能晚于结束时间", icon: "none" });
  } else {
    formData.start_time = dateTime;
  }
};

const onEndTimeConfirm = (e: { result: string }) => {
  const dateTime = e.result;
  if (
    formData.start_time &&
    new Date(dateTime) <= new Date(formData.start_time)
  ) {
    uni.showToast({ title: "结束时间不能早于开始时间", icon: "none" });
  } else {
    formData.end_time = dateTime;
  }
};

// --- 性别年龄选择 ---
const showGenderAgeModal = () => {
  showGenderAgeSelector.value = true;
};

const closeGenderAgeModal = () => {
  showGenderAgeSelector.value = false;
};

const onGenderAgeConfirm = (gender: number, minAge: number, maxAge: number) => {
  formData.gender = gender;
  formData.age_min = minAge;
  formData.age_max = maxAge;
};

// --- 地址选择 ---
const selectLocation = () => {
  uni.chooseLocation({
    success: (res) => {
      formData.address_name = res.name || "";
      formData.address = res.address || "";
      formData.latitude = res.latitude;
      formData.longitude = res.longitude;
    },
  });
};

// --- 其他表单逻辑 ---
const resetForm = () => {
  Object.assign(formData, initialFormData);
  // 重置picker索引
  salaryUnitPickerValue.value = 1;
  settlementPickerValue.value = 0;
  educationPickerValue.value = 0;
  experiencePickerValue.value = 0;
  approvalModePickerValue.value = 0;
  checkInMethodPickerValue.value = 0;
  agreedToTerms.value = false;
};

const toggleAgreement = () => {
  agreedToTerms.value = !agreedToTerms.value;
};

const canSubmit = computed(() => {
  const requiredFields: (keyof CreateGigRequest)[] = [
    "title",
    "start_time",
    "end_time",
    "address",
    "detail_address",
    "people_count",
    "salary",
    "settlement",
    "age_min",
    "age_max",
    "approval_mode",
    "check_in_method",
    "contact_name",
    "contact_phone",
  ];
  return (
    requiredFields.every((field) => !!formData[field]) &&
    formData.age_min >= 16 &&
    formData.age_max <= 65 &&
    formData.age_min < formData.age_max &&
    agreedToTerms.value
  );
});

// --- 表单提交 ---
const handleSubmit = async () => {
  console.log("提交表单数据:", formData);
  if (!canSubmit.value) {
    console.warn("表单不满足提交条件");
    return;
  }

  try {
    // 使用ThorUI表单校验 - 正确的调用方式
    const validationResult = await formRef.value.validate(formData, formRules, true);
    
    console.log('表单验证结果:', validationResult);
    
    if (!validationResult.isPass) {
      console.error('表单验证失败:', validationResult.errorMsg);
      
      // 显示第一个验证错误
      let errorMessage = '请检查表单信息';
      if (Array.isArray(validationResult.errorMsg) && validationResult.errorMsg.length > 0) {
        const firstError = validationResult.errorMsg[0];
        errorMessage = typeof firstError === 'object' ? firstError.msg : firstError;
      } else if (typeof validationResult.errorMsg === 'string') {
        errorMessage = validationResult.errorMsg;
      }
      showToast(errorMessage);
      return;
    }

    console.log('表单验证通过，开始业务逻辑验证');

    // 验证性别年龄是否已选择（自定义验证）
    if (!formData.gender && formData.gender !== 0) {
      showToast('请选择性别要求');
      return;
    }
    if (!formData.age_min || !formData.age_max) {
      showToast('请选择年龄范围');
      return;
    }

    // 额外的业务逻辑验证
    if (new Date(formData.start_time) >= new Date(formData.end_time)) {
      console.error("时间验证失败: 开始时间晚于结束时间");
      showToast("开始时间必须早于结束时间");
      return;
    }

    // 年龄范围验证
    if (formData.age_min >= formData.age_max) {
      console.error("年龄验证失败: 最小年龄大于等于最大年龄");
      showToast("最小年龄必须小于最大年龄");
      return;
    }

    if (formData.age_min < 16 || formData.age_max > 65) {
      console.error("年龄验证失败: 年龄范围超出限制");
      showToast("年龄范围必须在16-65岁之间");
      return;
    }

    // 审核方式与招聘人数的关联提示
    if (formData.approval_mode === "auto" && formData.people_count > 10) {
      console.log("显示自动录用确认提示");
      const result = await new Promise((resolve) => {
        uni.showModal({
          title: "提示",
          content:
            "招聘人数较多，建议使用手动审核方式以便更好筛选。是否继续使用自动录用？",
          success: (res) => resolve(res.confirm),
        });
      });
      if (!result) {
        console.log("用户取消自动录用");
        return;
      }
    }

    console.log("开始准备提交数据");

    // 准备提交数据
    const requestData = JSON.parse(JSON.stringify(formData));
    if (requestData.salary) {
      requestData.salary = Math.round(requestData.salary * 100);
    }

    console.log("最终提交数据:", requestData);

    // 使用统一的提交工具
    const success = await submitForm(
      () => createGig(requestData),
      "发布成功！",
      () => {
        resetForm();
        setTimeout(() => navigateBack(), 1500);
      }
    );

    console.log("提交结果:", success);
  } catch (validationError: any) {
    console.error("表单提交过程中发生错误:", validationError);

    // 处理不同类型的错误
    let errorMessage = "请检查表单信息";

    if (validationError.isPass === false) {
      // ThorUI验证错误
      errorMessage =
        validationError.errorMsg || validationError.message || errorMessage;
    } else if (validationError.message) {
      // 标准错误对象
      errorMessage = validationError.message;
    } else if (validationError.msg) {
      // 自定义错误格式
      errorMessage = validationError.msg;
    } else if (typeof validationError === "string") {
      // 字符串错误
      errorMessage = validationError;
    }

    console.error("错误信息:", errorMessage);
    showToast(errorMessage);
  }
};

// 开发环境填充测试数据
onMounted(() => {
  if (import.meta.env.DEV) {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const startTime = new Date(tomorrow);
    startTime.setHours(9, 0, 0, 0);
    const endTime = new Date(tomorrow);
    endTime.setHours(18, 0, 0, 0);

    Object.assign(formData, {
      title: "测试零工-咖啡店服务员",
      start_time: startTime.toISOString().slice(0, 16).replace("T", " "),
      end_time: endTime.toISOString().slice(0, 16).replace("T", " "),
      address_name: "星巴克（软件园店）",
      address: "福建省厦门市思明区软件园二期望海路10号",
      detail_address: "一楼大厅",
      latitude: 24.4856,
      longitude: 118.18,
      people_count: 2,
      salary: 25,
      salary_unit: 1, // 小时
      settlement: 1, // 日结
      age_min: 16,
      age_max: 65, // 默认为不限年龄
      approval_mode: "manual", // 手动审核
      check_in_method: "gps", // GPS打卡
      description:
        "负责点单、制作咖啡、保持店面整洁。要求有责任心，服务态度好。",
      contact_name: "王经理",
      contact_phone: "13800138000",
    });
    // 同步picker的索引
    salaryUnitPickerValue.value =
      GIG_SALARY_UNIT_OPTIONS?.findIndex(
        (o) => o.value === formData.salary_unit
      ) || 0;
    approvalModePickerValue.value =
      GIG_APPROVAL_MODE_OPTIONS?.findIndex(
        (o) => o.value === formData.approval_mode
      ) || 0;
    checkInMethodPickerValue.value =
      GIG_CHECKIN_METHOD_OPTIONS?.findIndex(
        (o) => o.value === formData.check_in_method
      ) || 0;
  }
});
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.content {
  margin: var(--spacing-10);
  padding-bottom: 160rpx; /* 为底部操作栏留出空间 */
}

.card {
  margin-bottom: 24rpx;
}

.description-card {
  padding: var(--spacing-16);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  padding: 16rpx;
}

.picker-input {
  height: 100%;
  display: flex;
  align-items: center;
  color: var(--text-base);
  font-size: var(--font-size-base);
  text-align: right;
  width: 100%;
  padding-right: 8rpx;

  &.placeholder {
    color: var(--text-grey);
  }
}

.salary-input-group {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8rpx;
}

.salary-input {
  flex: 1;
  font-size: var(--font-size-base);
  color: var(--text-base);
  text-align: right;
}

.salary-unit {
  font-size: var(--font-size-base);
  color: var(--text-base);
  flex-shrink: 0;
}

.salary-picker {
  flex-shrink: 0;
  .picker-input {
    padding-right: 0;
    border-left: 1rpx solid var(--border-color);
    padding-left: 16rpx;
    min-width: 120rpx; /* 保证最小宽度 */
    justify-content: space-between; /* 文字和箭头分布 */
    gap: 8rpx;

    .picker-arrow {
      font-size: 20rpx;
      color: var(--text-info);
    }
  }
}

.description-section {
  .description-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-12);

    text {
      font-size: var(--font-size-base);
      font-weight: var(--font-weight-medium);
      color: var(--text-base);
    }

    .char-count {
      font-size: var(--font-size-sm);
      color: var(--text-info);
    }
  }
}

.description-textarea {
  width: 100%;
  min-height: 200rpx;
  padding: var(--spacing-12);
  border: 1rpx solid var(--border-color);
  border-radius: var(--radius);
  font-size: var(--font-size-base);
  color: var(--text-base);
  background-color: var(--bg-input);
  line-height: var(--line-height-loose);
  box-sizing: border-box;
}

.textarea-placeholder {
  color: var(--text-grey);
}

.agreement-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-8);
  padding: var(--spacing-16) 0;
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-card);
  flex-shrink: 0;
  transition: all 0.3s ease;

  &.checked {
    background-color: var(--primary);
    border-color: var(--primary);
    color: var(--text-inverse);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
  }
}

.agreement-text {
  font-size: var(--font-size-base);
  color: var(--text-base);
  line-height: var(--line-height-loose);
  flex: 1;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-12) var(--spacing-16);
  background-color: var(--bg-card);
  border-top: 1rpx solid var(--border-color);
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
  z-index: 100;
}

/* 性别年龄选择样式 */
.gender-age-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8rpx 0;

  .gender-age-text {
    color: var(--text-base);
    font-size: var(--font-size-base);

    &.placeholder {
      color: var(--text-grey);
    }
  }

  .arrow-icon {
    color: var(--text-info);
    font-size: 24rpx;
  }
}

/* 选项说明样式 */
.option-description {
  margin: 16rpx 32rpx;
  padding: 24rpx;
  background-color: var(--bg-input);
  border-radius: var(--radius);
  border-left: 6rpx solid var(--primary);

  .description-text {
    font-size: 26rpx;
    color: var(--text-info);
    line-height: var(--line-height-loose);
  }
}
</style>
