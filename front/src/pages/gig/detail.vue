<template>
  <view class="gig-detail-page">
    <!-- 加载状态 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="hasError" class="error-container">
      <text class="error-text">{{ errorMessage }}</text>
      <button class="retry-button" @click="fetchGigDetail">重试</button>
    </view>

    <!-- 正常内容 -->
    <scroll-view v-else-if="gig" scroll-y class="detail-scroll-view">
      <!-- 头部英雄区域 - 重新设计 -->
      <view class="hero-section">
        <!-- 状态和紧急标识 -->
        <view class="status-row">
          <view
            v-if="gig.status"
            class="status-badge"
            :class="getGigStatusDetails(gig.status).class"
          >
            <text>{{ getGigStatusDetails(gig.status).text }}</text>
          </view>
          <view v-if="gig.is_urgent" class="urgent-badge">
            <text class="urgent-icon">🔥</text>
            <text class="urgent-text">紧急招聘</text>
          </view>
        </view>

        <!-- 核心信息区域 -->
        <view class="core-info">
          <text class="gig-title">{{ gig.title || "暂无标题" }}</text>

          <!-- 薪资突出显示 -->
          <view class="salary-highlight">
            <text class="salary-amount">{{
              formatSalary(gig.salary || 0, gig.salary_unit || 1)
            }}</text>
            <text class="salary-unit">{{
              getSalaryUnitText(gig.salary_unit)
            }}</text>
          </view>

          <!-- 关键时间信息 -->
          <view class="time-info">
            <view class="time-item">
              <text class="time-icon i-carbon-calendar"></text>
              <text class="time-text">{{
                formatWorkDate(gig.start_time, gig.end_time)
              }}</text>
            </view>
            <view class="time-item">
              <text class="time-icon i-carbon-time"></text>
              <text class="time-text">{{
                formatWorkTime(gig.start_time, gig.end_time)
              }}</text>
            </view>
          </view>
        </view>

        <!-- 快速信息网格 -->
        <view class="quick-info-grid">
          <view class="quick-info-item">
            <text class="quick-icon i-carbon-location"></text>
            <text class="quick-label">地点</text>
            <text class="quick-value">{{ gig.address_name || "待定" }}</text>
          </view>

          <view class="quick-info-item">
            <text class="quick-icon i-carbon-timer"></text>
            <text class="quick-label">时长</text>
            <text class="quick-value">{{
              formatWorkDurationFromMinutes(gig.work_duration) || "待定"
            }}</text>
          </view>

          <view v-if="!isPublisher" class="quick-info-item">
            <text class="quick-icon i-carbon-group-account"></text>
            <text class="quick-label">招聘</text>
            <text class="quick-value">{{ gig.people_count }}人</text>
          </view>

          <view v-else class="quick-info-item">
            <text class="quick-icon i-carbon-group-account"></text>
            <text class="quick-label">进度</text>
            <text class="quick-value"
              >{{ gig.current_people_count || 0 }}/{{
                gig.people_count
              }}人</text
            >
          </view>
        </view>
      </view>

      <!-- 详细信息卡片 -->
      <view class="detail-card">
        <view class="card-header">
          <text class="card-title">详细信息</text>
        </view>

        <view class="detail-grid">
          <view class="detail-row">
            <text class="detail-label">详细时间</text>
            <text class="detail-value">{{
              formatWorkTimeRange(gig.start_time, gig.end_time, "detailed") ||
              "时间待定"
            }}</text>
          </view>

          <view class="detail-row">
            <text class="detail-label">详细地址</text>
            <text class="detail-value">{{
              gig.full_address || "地点待定"
            }}</text>
          </view>

          <!-- 报名情况：仅发布者可见 -->
          <view v-if="isPublisher" class="detail-row">
            <text class="detail-label">报名情况</text>
            <text class="detail-value">
              已报名 {{ gig.current_people_count || 0 }} /
              {{ gig.people_count || 0 }} 人
            </text>
          </view>

          <view class="detail-row">
            <text class="detail-label">审批方式</text>
            <text class="detail-value">{{
              gig.approval_mode === "auto" ? "自动录用" : "手动审批"
            }}</text>
          </view>
        </view>
      </view>

      <!-- 任务描述卡片 -->
      <view class="description-card">
        <view class="card-header">
          <text class="card-title">任务描述</text>
        </view>

        <text class="description-text">{{
          gig.description || "暂无描述"
        }}</text>

        <!-- 图片展示 -->
        <view v-if="gig.images && gig.images.length > 0" class="image-gallery">
          <image
            v-for="(img, idx) in gig.images"
            :key="idx"
            :src="img"
            class="gallery-image"
            mode="aspectFill"
            @error="handleImageError"
          />
        </view>
      </view>

      <!-- 职位要求卡片 -->
      <view class="requirements-card">
        <view class="card-header">
          <text class="card-title">职位要求</text>
        </view>

        <view class="requirements-grid">
          <view class="requirement-item">
            <text class="req-label">性别要求</text>
            <text class="req-value">{{
              getGenderText(gig.gender) || "不限"
            }}</text>
          </view>

          <view class="requirement-item">
            <text class="req-label">年龄要求</text>
            <text class="req-value">{{
              `${gig.age_min || 16}-${gig.age_max || 65}岁`
            }}</text>
          </view>

          <view class="requirement-item">
            <text class="req-label">经验要求</text>
            <text class="req-value">{{
              getExperienceText(gig.experience) || "不限"
            }}</text>
          </view>

          <view class="requirement-item">
            <text class="req-label">学历要求</text>
            <text class="req-value">{{
              getEducationText(gig.education) || "不限"
            }}</text>
          </view>
        </view>
      </view>

      <!-- 联系方式卡片 -->
      <view v-if="shouldShowContact" class="contact-card">
        <view class="card-header">
          <text class="card-title">联系方式</text>
        </view>

        <view class="contact-list">
          <view class="contact-item">
            <text class="contact-icon i-carbon-user"></text>
            <view class="contact-info">
              <text class="contact-label">联系人</text>
              <text class="contact-value">{{
                gig.contact_name || "联系人待定"
              }}</text>
            </view>
          </view>

          <view class="contact-item" @tap="handleCallPhone">
            <text class="contact-icon i-carbon-phone"></text>
            <view class="contact-info">
              <text class="contact-label">联系电话</text>
              <text class="contact-value">{{
                gig.contact_phone || "电话待定"
              }}</text>
            </view>
            <text class="contact-action i-carbon-chevron-right"></text>
          </view>
        </view>
      </view>

      <!-- 联系方式权限提示 -->
      <view
        v-else-if="!isPublisher && gig.contact_phone"
        class="contact-hint-card"
      >
        <view class="card-header">
          <text class="card-title">联系方式</text>
        </view>

        <view class="contact-hint">
          <text class="hint-icon i-carbon-lock"></text>
          <view class="hint-content">
            <text class="hint-title">{{
              hasApplied ? "申请审核中" : "需要先报名"
            }}</text>
            <text class="hint-desc">
              {{
                hasApplied
                  ? "申请已提交，审核通过后可查看联系方式"
                  : "报名成功后可查看发布者联系方式"
              }}
            </text>
          </view>
        </view>
      </view>

      <!-- 发布者信息卡片 -->
      <view v-if="gig.publisher" class="publisher-card">
        <view class="card-header">
          <text class="card-title">发布者信息</text>
        </view>

        <view class="publisher-content" @tap="goToPublisherProfile">
          <image
            :src="gig.publisher.avatar || '/static/images/default-avatar.png'"
            class="publisher-avatar"
            @error="handleAvatarError"
          />
          <view class="publisher-info">
            <text class="publisher-name">{{
              gig.publisher.nickname || "匿名用户"
            }}</text>
            <text class="publisher-desc">点击查看详情</text>
          </view>
          <text class="publisher-arrow i-carbon-chevron-right"></text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="bottom-action-bar safe-area-inset-bottom">
      <!-- 发布者视角的操作按钮 -->
      <template v-if="isPublisher">
        <button
          class="secondary-action-button"
          @tap="handleEditGig"
          :disabled="!canEdit"
        >
          <text class="button-icon i-carbon-edit"></text>
          <text class="button-text">编辑</text>
        </button>

        <button
          class="primary-action-button"
          @tap="handleManageApplications"
          :class="publisherActionButton.type"
        >
          <text class="button-text">{{ publisherActionButton.text }}</text>
        </button>
      </template>

      <!-- 求职者视角的操作按钮 -->
      <template v-else>
        <!-- 联系发布者按钮（仅在有权限时显示） -->
        <button
          v-if="shouldShowContactButton"
          class="contact-button"
          @tap="handleContactEmployer"
        >
          <text class="button-icon i-carbon-phone"></text>
          <text class="button-text">联系发布者</text>
        </button>

        <!-- 主要操作按钮 -->
        <button
          class="primary-action-button"
          @click="handleApply"
          :disabled="applyButton.disabled"
          :class="[
            applyButton.type,
            { 'disabled-button': applyButton.disabled },
          ]"
        >
          <text class="button-text">{{ applyButton.text }}</text>
        </button>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getGig, applyForGig, checkApplicationStatus } from "@/api/gig";
import { type Gig, type CheckApplicationStatusResponse } from "@/types/gig";
import {
  GigStatus,
  GigApplicationStatus,
  getGigStatusDetails,
} from "@/constants/gig";
import {
  getGenderText,
  getExperienceText,
  getEducationText,
} from "@/utils/standards";
import { formatSalary } from "@/utils/gig";
import { formatWorkTimeRange } from "@/utils/format";
import { fetchSingle, submitForm } from "@/utils/api-helper";
import { showConfirm } from "@/utils/ui";
import { navigateTo } from "@/utils/navigation";
import { useUserStore } from "@/stores/user";

// 格式化工作时长（分钟转换为小时/天）
function formatWorkDurationFromMinutes(minutes: number): string {
  if (!minutes || minutes <= 0) return "待定";

  if (minutes < 60) {
    return `${minutes}分钟`;
  } else if (minutes < 1440) {
    // 小于24小时
    const hours = Math.round((minutes / 60) * 10) / 10; // 保留一位小数
    return `${hours}小时`;
  } else {
    const days = Math.round((minutes / 1440) * 10) / 10;
    return `${days}天`;
  }
}

// 数据状态
const gig = ref<Gig | null>(null);
const gigId = ref<number>(0);
const isLoading = ref(true);
const hasError = ref(false);
const errorMessage = ref("加载失败");

// 申请状态
const applicationStatusInfo = ref<CheckApplicationStatusResponse | null>(null);

// 用户状态
const userStore = useUserStore();
const isPublisher = ref(false); // 是否为发布者

// 计算属性：申请状态
const hasApplied = computed(() => {
  return applicationStatusInfo.value?.has_applied || !!gig.value?.has_applied;
});

const applicationStatus = computed(() => {
  return applicationStatusInfo.value?.application_status || null;
});

// 编辑工作
const handleEditGig = () => {
  if (!gig.value) return;

  navigateTo(`/pages/gig/publish?id=${gig.value.id}&mode=edit`);
};

// 管理申请
const handleManageApplications = () => {
  if (!gig.value) return;

  const status = gig.value.status;

  switch (status) {
    case GigStatus.Recruiting:
    case GigStatus.InProgress:
      navigateTo(`/pages/gig/applications?gigId=${gig.value.id}`);
      break;

    case GigStatus.Completed:
      navigateTo(`/pages/gig/records?gigId=${gig.value.id}`);
      break;

    case GigStatus.Paused:
      // 恢复招聘
      handleResumeRecruitment();
      break;

    case GigStatus.Closed:
      // 重新发布
      handleRepublish();
      break;

    default:
      navigateTo(`/pages/gig/applications?gigId=${gig.value.id}`);
  }
};

// 恢复招聘
const handleResumeRecruitment = async () => {
  if (!gig.value) return;

  try {
    await showConfirm("确认恢复招聘？", "恢复后工作将重新开始招聘");

    uni.showToast({
      title: "已恢复招聘",
      icon: "success",
    });

    // 重新加载数据
    await fetchGigDetail();
  } catch (error) {
    console.error("恢复招聘失败:", error);
  }
};

// 重新发布
const handleRepublish = async () => {
  if (!gig.value) return;

  try {
    await showConfirm("确认重新发布？", "将基于当前信息重新发布工作");

    // 跳转到发布页面，预填充当前信息
    navigateTo(`/pages/gig/publish?id=${gig.value.id}&mode=republish`);
  } catch (error) {
    console.error("重新发布失败:", error);
  }
};

onLoad(async (options) => {
  if (!options?.id) {
    uni.showToast({ title: "参数错误", icon: "none" });
    uni.navigateBack();
    return;
  }

  const id = parseInt(options.id, 10);
  if (isNaN(id) || id <= 0) {
    uni.showToast({ title: "无效的零工ID", icon: "none" });
    uni.navigateBack();
    return;
  }

  gigId.value = id;
  await fetchGigDetail();
});

async function fetchGigDetail() {
  if (!gigId.value) return;

  try {
    isLoading.value = true;
    hasError.value = false;

    // 并行获取零工详情和申请状态
    const [gigResult, statusResult] = await Promise.allSettled([
      fetchSingle(() => getGig(gigId.value), "获取零工详情失败"),
      fetchApplicationStatus(),
    ]);

    // 处理零工详情
    if (gigResult.status === "fulfilled" && gigResult.value?.data) {
      gig.value = gigResult.value.data;

      // 检查用户是否为发布者
      const { user } = userStore;
      isPublisher.value = !!(user?.id && gig.value.user_id === user.id);
    } else {
      throw new Error("无法获取零工详情");
    }

    // 处理申请状态（失败不影响主流程）
    if (statusResult.status === "fulfilled") {
      // 申请状态获取成功，已在 fetchApplicationStatus 中处理
    } else {
      console.warn("获取申请状态失败:", statusResult.reason);
    }
  } catch (error) {
    console.error("获取零工详情失败:", error);
    hasError.value = true;
    errorMessage.value =
      error instanceof Error ? error.message : "加载失败，请稍后再试";
  } finally {
    isLoading.value = false;
  }
}

// 获取申请状态
async function fetchApplicationStatus() {
  if (!gigId.value) return;

  const { user } = userStore;
  if (!user?.id) return; // 未登录用户不需要获取申请状态

  try {
    const result = await checkApplicationStatus(gigId.value);
    if (result.code === 0 && result.data) {
      applicationStatusInfo.value = result.data;
    }
  } catch (error) {
    console.warn("获取申请状态失败:", error);
    // 申请状态获取失败不影响主流程
  }
}

// 错误处理函数
const handleImageError = (e: any) => {
  console.warn("图片加载失败:", e);
  // 可以在这里设置默认图片
};

const handleAvatarError = (e: any) => {
  console.warn("头像加载失败:", e);
  // 可以设置默认头像
};

// 底部操作按钮逻辑
const applyButton = computed(() => {
  if (isLoading.value) {
    return { text: "加载中...", disabled: true, type: "loading" };
  }

  if (!gig.value) {
    return { text: "数据加载失败", disabled: true, type: "error" };
  }

  // 发布者看到的按钮
  if (isPublisher.value) {
    return { text: "管理申请", disabled: false, type: "publisher" };
  }

  // 普通用户已申请的情况
  if (hasApplied.value) {
    return { text: "已报名", disabled: true, type: "applied" };
  }

  // 普通用户未申请的情况
  switch (gig.value.status) {
    case GigStatus.Recruiting:
      return { text: "立即报名", disabled: false, type: "primary" };
    case GigStatus.Paused:
      return { text: "暂停招募", disabled: true, type: "paused" };
    case GigStatus.Locked:
      return { text: "招募截止", disabled: true, type: "locked" };
    case GigStatus.InProgress:
      return { text: "进行中", disabled: true, type: "progress" };
    case GigStatus.Completed:
      return { text: "已完成", disabled: true, type: "completed" };
    case GigStatus.Closed:
      return { text: "已关闭", disabled: true, type: "closed" };
    default:
      return { text: "无法报名", disabled: true, type: "disabled" };
  }
});

// 是否显示联系发布者按钮
const shouldShowContactButton = computed(() => {
  if (!gig.value || isPublisher.value) return false;

  // 只有已申请且审核通过的用户才能看到联系按钮
  return (
    hasApplied.value &&
    applicationStatus.value === GigApplicationStatus.Confirmed
  );
});

// 发布者操作按钮状态
const publisherActionButton = computed(() => {
  if (!gig.value) {
    return { text: "加载中...", type: "disabled" };
  }

  const status = gig.value.status;
  const currentCount = gig.value.current_people_count || 0;

  switch (status) {
    case GigStatus.Recruiting:
      if (currentCount > 0) {
        return { text: `管理申请 (${currentCount})`, type: "primary" };
      } else {
        return { text: "管理申请", type: "secondary" };
      }

    case GigStatus.InProgress:
      return { text: "管理工作", type: "primary" };

    case GigStatus.Completed:
      return { text: "查看记录", type: "secondary" };

    case GigStatus.Paused:
      return { text: "恢复招聘", type: "primary" };

    case GigStatus.Closed:
      return { text: "重新发布", type: "primary" };

    default:
      return { text: "管理", type: "secondary" };
  }
});

// 是否可以编辑
const canEdit = computed(() => {
  if (!gig.value || !isPublisher.value) return false;

  const status = gig.value.status;
  return status === GigStatus.Recruiting || status === GigStatus.Paused;
});

async function handleApply() {
  if (!gig.value || applyButton.value.disabled) return;

  const { user } = userStore;

  // 发布者点击管理申请
  if (isPublisher.value) {
    navigateTo(`/pages/gig/applicants?gigId=${gigId.value}`);
    return;
  }

  // 已申请用户的操作
  if (hasApplied.value) {
    // 目前无法判断具体申请状态，暂时显示提示
    uni.showToast({
      title: "您已申请过此工作，请耐心等待审核结果",
      icon: "none",
      duration: 2000,
    });
    return;
  }

  // 未申请用户的报名操作
  if (!user?.nickname || !user?.mobile) {
    const confirmed = await showConfirm({
      title: "提示",
      content: "请先在'我的'页面完善您的姓名和联系方式再进行报名。",
      confirmText: "去完善",
      cancelText: "取消",
    });

    if (confirmed) {
      navigateTo("/pages/mine/profile");
    }
    return;
  }

  const confirmed = await showConfirm({
    title: "确认报名",
    content: `您确定要报名【${gig.value.title}】吗？`,
    confirmText: "确认报名",
  });

  if (confirmed) {
    const success = await submitForm(
      () =>
        applyForGig({
          gig_id: gig.value!.id,
          message: "我对此很感兴趣，希望获得这个机会！",
          applicant_name: user.nickname!,
          applicant_phone: user.mobile!,
        }),
      "报名成功！",
      async () => {
        // 重新获取数据以更新状态
        await fetchGigDetail();
      }
    );
  }
}

// 拨打电话
async function handleCallPhone() {
  if (!gig.value?.contact_phone) {
    uni.showToast({ title: "暂无联系方式", icon: "none" });
    return;
  }

  uni.makePhoneCall({
    phoneNumber: gig.value.contact_phone,
    fail: (err) => {
      console.error("拨打电话失败:", err);
      uni.showToast({ title: "拨打电话失败", icon: "none" });
    },
  });
}

// 联系发布者
async function handleContactEmployer() {
  if (!gig.value?.contact_phone) {
    uni.showToast({ title: "暂无联系方式", icon: "none" });
    return;
  }

  const confirmed = await showConfirm({
    title: "联系发布者",
    content: `是否拨打 ${gig.value.contact_phone}？`,
    confirmText: "拨打电话",
    cancelText: "取消",
  });

  if (confirmed) {
    handleCallPhone();
  }
}

// 查看发布者资料
function goToPublisherProfile() {
  if (!gig.value?.publisher?.id) {
    uni.showToast({ title: "无法查看发布者信息", icon: "none" });
    return;
  }

  // TODO: 跳转到发布者详情页面
  uni.showToast({ title: "发布者详情页面开发中", icon: "none" });
}

// 取消申请
async function handleCancelApplication() {
  const confirmed = await showConfirm({
    title: "确认取消",
    content: "您确定要取消这个申请吗？取消后可能需要重新申请。",
    confirmText: "确认取消",
    cancelText: "继续保持",
  });

  if (confirmed) {
    // TODO: 调用取消申请的API
    // await cancelApplication(gigId.value);
    uni.showToast({ title: "取消成功", icon: "success" });
    // 重新获取数据以更新状态
    fetchGigDetail();
  }
}
// 计算属性：是否显示联系方式
const shouldShowContact = computed(() => {
  if (!gig.value) return false;

  // 发布者始终可见
  if (isPublisher.value) return true;

  // 普通用户只有在申请被确认后才能看到联系方式
  return (
    hasApplied.value &&
    applicationStatus.value === GigApplicationStatus.Confirmed
  );
});

// 处理沟通功能
const handleChat = () => {
  // TODO: 实现聊天功能
  uni.showToast({ title: "聊天功能开发中", icon: "none" });
};
</script>

<style lang="scss" scoped>
.gig-detail-page {
  width: 100vw;
  min-height: 100vh;
  background: linear-gradient(
    180deg,
    var(--primary-50) 0%,
    var(--bg-page) 200rpx
  );
  padding-bottom: 160rpx;
  box-sizing: border-box;
  overflow-x: hidden;

  .detail-scroll-view {
    width: 100%;
    padding: 24rpx 24rpx 0;
    box-sizing: border-box;
  }

  // 通用卡片样式
  .hero-card,
  .detail-card,
  .description-card,
  .requirements-card,
  .contact-card,
  .contact-hint-card,
  .publisher-card {
    width: 100%;
    background: var(--bg-card);
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    overflow: hidden;
    box-sizing: border-box;
  }

  .card-header {
    padding: 32rpx 32rpx 24rpx;
    border-bottom: 1rpx solid var(--border-color);

    .card-title {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--text-base);
    }
  }

  // 头部英雄卡片
  .hero-card {
    position: relative;
    padding: 40rpx 32rpx 32rpx;
    background: linear-gradient(135deg, var(--bg-card) 0%, #fafafa 100%);

    .status-badge {
      position: absolute;
      top: 24rpx;
      right: 24rpx;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      font-weight: 600;
      color: white;

      &.status-recruiting {
        background: var(--text-green);
      }
      &.status-paused {
        background: var(--text-yellow);
      }
      &.status-locked {
        background: var(--text-blue);
      }
      &.status-ongoing {
        background: var(--text-blue);
      }
      &.status-completed {
        background: var(--text-info);
      }
      &.status-closed {
        background: var(--text-red);
      }
    }

    .hero-content {
      margin-bottom: 32rpx;

      .gig-title {
        font-size: 40rpx;
        font-weight: 700;
        color: var(--text-base);
        line-height: 1.3;
        margin-bottom: 16rpx;
        padding-right: 120rpx;
      }

      .salary-section {
        display: flex;
        align-items: baseline;
        gap: 12rpx;

        .salary-amount {
          font-size: 48rpx;
          font-weight: 700;
          color: var(--primary);
        }

        .salary-label {
          font-size: 28rpx;
          color: var(--text-secondary);
          font-weight: 500;
        }
      }
    }

    .key-info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24rpx 16rpx;
      margin-bottom: 32rpx;
      width: 100%;

      .info-item {
        display: flex;
        align-items: flex-start;
        gap: 12rpx;
        min-width: 0;

        .info-icon {
          font-size: 32rpx;
          color: var(--primary);
          margin-top: 4rpx;
          flex-shrink: 0;
        }

        .info-content {
          flex: 1;
          min-width: 0;
          overflow: hidden;

          .info-label {
            font-size: 24rpx;
            color: var(--text-info);
            margin-bottom: 4rpx;
            display: block;
          }

          .info-value {
            font-size: 28rpx;
            color: var(--text-base);
            font-weight: 500;
            word-break: break-all;
            overflow-wrap: break-word;
          }
        }
      }
    }

    .tags-section {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;

      .urgent-tag {
        background: linear-gradient(135deg, var(--text-red), #ff6b6b);
        color: white;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 600;
        box-shadow: 0 2rpx 8rpx rgba(245, 44, 55, 0.3);

        .urgent-text {
          color: white;
        }
      }

      .tag {
        background: var(--bg-tag);
        color: var(--text-secondary);
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;
      }
    }
    // 详细信息卡片
    .detail-card {
      .detail-grid {
        padding: 32rpx;

        .detail-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20rpx 0;
          border-bottom: 1rpx solid var(--border-color);

          &:last-child {
            border-bottom: none;
          }

          .detail-label {
            font-size: 28rpx;
            color: var(--text-secondary);
            font-weight: 500;
          }

          .detail-value {
            font-size: 28rpx;
            color: var(--text-base);
            font-weight: 500;
            text-align: right;
            flex: 1;
            margin-left: 24rpx;
          }
        }
      }
    }
    // 任务描述卡片
    .description-card {
      .description-text {
        padding: 32rpx;
        font-size: 30rpx;
        line-height: 1.6;
        color: var(--text-base);
      }

      .image-gallery {
        padding: 0 32rpx 32rpx;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200rpx, 1fr));
        gap: 16rpx;

        .gallery-image {
          width: 100%;
          height: 200rpx;
          border-radius: 16rpx;
          background: var(--bg-tag);
        }
      }
    }

    // 职位要求卡片
    .requirements-card {
      .requirements-grid {
        padding: 32rpx;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 32rpx 24rpx;

        .requirement-item {
          .req-label {
            font-size: 26rpx;
            color: var(--text-info);
            margin-bottom: 8rpx;
            display: block;
          }

          .req-value {
            font-size: 28rpx;
            color: var(--text-base);
            font-weight: 500;
          }
        }
      }
    }
    // 联系方式卡片
    .contact-card {
      .contact-list {
        padding: 32rpx;

        .contact-item {
          display: flex;
          align-items: center;
          padding: 20rpx 0;
          border-bottom: 1rpx solid var(--border-color);

          &:last-child {
            border-bottom: none;
          }

          .contact-icon {
            font-size: 32rpx;
            color: var(--primary);
            margin-right: 16rpx;
            flex-shrink: 0;
          }

          .contact-info {
            flex: 1;

            .contact-label {
              font-size: 26rpx;
              color: var(--text-info);
              margin-bottom: 4rpx;
              display: block;
            }

            .contact-value {
              font-size: 28rpx;
              color: var(--text-base);
              font-weight: 500;
            }
          }

          .contact-action {
            font-size: 24rpx;
            color: var(--text-info);
          }
        }
      }
    }

    // 联系方式提示卡片
    .contact-hint-card {
      .contact-hint {
        padding: 32rpx;
        display: flex;
        align-items: flex-start;
        gap: 16rpx;
        background: var(--bg-tag);

        .hint-icon {
          font-size: 32rpx;
          color: var(--text-info);
          margin-top: 4rpx;
          flex-shrink: 0;
        }

        .hint-content {
          flex: 1;

          .hint-title {
            font-size: 28rpx;
            color: var(--text-base);
            font-weight: 600;
            margin-bottom: 8rpx;
            display: block;
          }

          .hint-desc {
            font-size: 26rpx;
            color: var(--text-secondary);
            line-height: 1.5;
          }
        }
      }
    }

    // 发布者信息卡片
    .publisher-card {
      .publisher-content {
        padding: 32rpx;
        display: flex;
        align-items: center;
        transition: background-color 0.2s ease;

        &:active {
          background: var(--bg-tag);
        }

        .publisher-avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          background: var(--bg-tag);
        }

        .publisher-info {
          flex: 1;

          .publisher-name {
            font-size: 30rpx;
            color: var(--text-base);
            font-weight: 600;
            margin-bottom: 4rpx;
            display: block;
          }

          .publisher-desc {
            font-size: 26rpx;
            color: var(--text-info);
          }
        }

        .publisher-arrow {
          font-size: 24rpx;
          color: var(--text-info);
        }
      }
    }
    // 底部操作栏
    .bottom-action-bar {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      align-items: center;
      gap: 16rpx;
      padding: 24rpx 24rpx;
      background: var(--bg-card);
      border-top: 1rpx solid var(--border-color);
      box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
      z-index: 100;

      .contact-button,
      .secondary-action-button {
        flex: 0 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8rpx;
        height: 88rpx;
        padding: 0 32rpx;
        background: var(--bg-tag);
        color: var(--text-base);
        border-radius: 44rpx;
        border: none;
        font-size: 28rpx;
        font-weight: 500;
        transition: all 0.3s ease;

        &:active {
          background: var(--text-info);
          color: white;
          transform: scale(0.98);
        }

        &:disabled {
          background: var(--bg-disabled);
          color: var(--text-disabled);
          cursor: not-allowed;
        }

        .button-icon {
          font-size: 32rpx;
        }
      }

      .primary-action-button {
        flex: 1;
        height: 88rpx;
        border-radius: 44rpx;
        border: none;
        font-size: 32rpx;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        .button-text {
          position: relative;
          z-index: 2;
        }

        // 按钮类型样式
        &.primary {
          background: linear-gradient(135deg, var(--primary), #ff8533);
          color: white;
          box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3);

          &:active {
            transform: translateY(2rpx);
            box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.4);
          }
        }

        &.secondary {
          background: var(--bg-secondary);
          color: var(--text-base);

          &:active {
            background: var(--text-info);
            color: white;
          }
        }

        &.publisher {
          background: linear-gradient(135deg, var(--text-blue), #4f9eff);
          color: white;
          box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
        }

        &.applied {
          background: var(--bg-success-light);
          color: var(--text-green);
          border: 2rpx solid var(--text-green);
        }

        &.disabled-button {
          background: var(--bg-tag);
          color: var(--text-info);
          box-shadow: none;
        }

        &.paused,
        &.locked,
        &.progress,
        &.completed,
        &.closed {
          background: var(--bg-tag);
          color: var(--text-info);
        }

        // 按钮动画效果
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
          transition: left 0.5s ease;
        }

        &:active::before {
          left: 100%;
        }
      }
    }
    // 加载和错误状态样式
    .loading-container,
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 60vh;
      padding: 80rpx 40rpx;
      text-align: center;

      .loading-spinner {
        width: 80rpx;
        height: 80rpx;
        border: 6rpx solid var(--bg-tag);
        border-top: 6rpx solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 32rpx;
      }

      .loading-text,
      .error-text {
        font-size: 30rpx;
        color: var(--text-secondary);
        margin-bottom: 32rpx;
        line-height: 1.5;
      }

      .retry-button {
        padding: 20rpx 40rpx;
        background: linear-gradient(135deg, var(--primary), #ff8533);
        color: white;
        border-radius: 44rpx;
        border: none;
        font-size: 28rpx;
        font-weight: 600;
        box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3);
        transition: all 0.3s ease;

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.4);
        }
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    // 响应式设计
    @media (max-width: 750rpx) {
      .hero-card .key-info-grid {
        grid-template-columns: 1fr;
        gap: 20rpx;
      }

      .requirements-card .requirements-grid {
        grid-template-columns: 1fr;
        gap: 24rpx;
      }

      .description-card .image-gallery {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    // 通用动画效果
    .hero-card,
    .detail-card,
    .description-card,
    .requirements-card,
    .contact-card,
    .contact-hint-card,
    .publisher-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:active {
        transform: translateY(2rpx);
      }
    }
  }
}
</style>
