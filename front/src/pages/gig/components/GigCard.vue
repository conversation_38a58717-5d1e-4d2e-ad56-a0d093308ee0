<template>
  <view class="gig-card" @tap="handleCardClick">
    <!-- 紧急标识 -->
    <view v-if="gig.is_urgent" class="urgent-badge">
      <text class="urgent-text">紧急</text>
    </view>

    <!-- 卡片主体内容 -->
    <view class="card-content">
      <!-- 头部：标题和薪资 -->
      <view class="card-header">
        <view class="title-section">
          <text class="gig-title">{{ gig.title }}</text>
          <text v-if="gig.description" class="gig-desc">{{ truncatedDescription }}</text>
        </view>
        <view class="salary-section">
          <text class="salary-amount">{{ formattedSalary }}</text>
        </view>
      </view>

      <!-- 关键信息网格 -->
      <view class="key-info-grid">
        <!-- 工作时间 -->
        <view class="info-item">
          <text class="info-icon i-carbon-time"></text>
          <view class="info-content">
            <text class="info-label">工作时间</text>
            <text class="info-value">{{ formattedWorkTime }}</text>
          </view>
        </view>

        <!-- 工作地点 -->
        <view class="info-item">
          <text class="info-icon i-carbon-location"></text>
          <view class="info-content">
            <text class="info-label">工作地点</text>
            <text class="info-value">{{ gig.address_name || "地点待定" }}</text>
          </view>
        </view>

        <!-- 工作时长 -->
        <view class="info-item">
          <text class="info-icon i-carbon-timer"></text>
          <view class="info-content">
            <text class="info-label">工作时长</text>
            <text class="info-value">{{ formattedWorkDuration }}</text>
          </view>
        </view>

        <!-- 招聘人数：仅发布者模式显示 -->
        <view v-if="mode === 'recruiter'" class="info-item">
          <text class="info-icon i-carbon-group-account"></text>
          <view class="info-content">
            <text class="info-label">招聘人数</text>
            <text class="info-value">{{ gig.current_people_count || 0 }}/{{ gig.people_count }}人</text>
          </view>
        </view>
      </view>

      <!-- 标签区域 -->
      <view v-if="hasVisibleTags" class="tags-section">
        <view v-for="(tag, index) in gig.tags" :key="index" class="tag">
          {{ tag }}
        </view>
        <view v-if="gig.experience" class="tag tag-requirement">
          {{ getExperienceText(gig.experience) }}
        </view>
        <view v-if="gig.gender" class="tag tag-requirement">
          {{ getGenderText(gig.gender) }}
        </view>
        <view v-if="ageRange" class="tag tag-requirement">
          {{ ageRange }}
        </view>
      </view>

      <!-- 底部操作区域 -->
      <view class="card-footer">
        <!-- 发布时间和报名情况 -->
        <view class="footer-info">
          <text class="publish-time">{{ formatRelativeTime(gig.created_at) }}</text>
          <!-- 报名情况：仅发布者模式显示 -->
          <text v-if="mode === 'recruiter'" class="apply-count">已报名 {{ gig.current_people_count || 0 }}/{{ gig.people_count }}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="action-section">
          <!-- 申请状态显示（管理模式） -->
          <view v-if="showStatus && applicationStatus" class="status-badge" :class="statusClass">
            <text class="status-text">{{ statusText }}</text>
          </view>

          <!-- 立即申请按钮（求职者模式） -->
          <button
            v-else-if="mode === 'seeker' && canApply"
            class="apply-button"
            @tap.stop="handleApply"
          >
            <text class="apply-text">{{ applyButtonText }}</text>
          </button>

          <!-- 查看详情按钮（其他情况） -->
          <button v-else class="detail-button" @tap.stop="handleViewDetail">
            <text class="detail-text">查看详情</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { Gig } from "@/types/gig";
import {
  formatSalary,
  getApplicationStatusText,
  getApplicationStatusClass,
} from "@/utils/gig";
import { GigStatus, GigApplicationStatus, getGigStatusDetails } from "@/constants/gig";
import { getGenderText, getExperienceText } from "@/utils/standards";
import { formatAgeRange } from "@/utils/format";
import { formatWorkDuration, formatWorkTimeRange } from "@/utils";
import { formatRelativeTime } from "@/utils/date";

interface Props {
  gig: Gig;
  mode?: "seeker" | "recruiter";
  showStatus?: boolean;
  showStats?: boolean;
  applicationStatus?: GigApplicationStatus;
}

const props = withDefaults(defineProps<Props>(), {
  mode: "seeker",
  showStatus: false,
  showStats: false,
});

// 定义事件
const emit = defineEmits<{
  apply: [gig: Gig];
  viewDetail: [gig: Gig];
  cardClick: [gig: Gig];
}>();

// 格式化薪酬显示
const formattedSalary = computed(() => {
  return formatSalary(props.gig.salary, props.gig.salary_unit);
});

// 格式化工作时间显示
const formattedWorkTime = computed(() => {
  return formatWorkTimeRange(props.gig.start_time, props.gig.end_time, "simple") || "时间待定";
});

// 格式化工作时长显示（从分钟转换）
const formattedWorkDuration = computed(() => {
  const minutes = props.gig.work_duration;
  if (!minutes || minutes <= 0) return "待定";

  if (minutes < 60) {
    return `${minutes}分钟`;
  } else if (minutes < 1440) { // 小于24小时
    const hours = Math.round(minutes / 60 * 10) / 10;
    return `${hours}小时`;
  } else {
    const days = Math.round(minutes / 1440 * 10) / 10;
    return `${days}天`;
  }
});

// 截断的描述
const truncatedDescription = computed(() => {
  if (!props.gig.description) return "";
  return props.gig.description.length > 60
    ? props.gig.description.substring(0, 60) + "..."
    : props.gig.description;
});

// 年龄范围显示
const ageRange = computed(() => {
  return formatAgeRange(props.gig.age_min, props.gig.age_max);
});

// 是否有可见的标签
const hasVisibleTags = computed(() => {
  return (props.gig.tags && props.gig.tags.length > 0) ||
         props.gig.experience ||
         props.gig.gender ||
         ageRange.value;
});

// 是否可以申请
const canApply = computed(() => {
  return props.gig.status === GigStatus.Recruiting && !props.gig.has_applied;
});

// 申请按钮文本
const applyButtonText = computed(() => {
  if (props.gig.has_applied) return "已申请";
  if (props.gig.status !== GigStatus.Recruiting) return "不可申请";
  return props.gig.is_urgent ? "抢单" : "立即申请";
});

// 申请状态样式类
const statusClass = computed(() => {
  if (!props.applicationStatus) return "";
  return getApplicationStatusClass(props.applicationStatus, props.mode);
});

// 申请状态文本
const statusText = computed(() => {
  if (!props.applicationStatus) return "";
  return getApplicationStatusText(props.applicationStatus, props.mode);
});

// 事件处理
const handleCardClick = () => {
  emit('cardClick', props.gig);
};

const handleApply = () => {
  emit('apply', props.gig);
};

const handleViewDetail = () => {
  emit('viewDetail', props.gig);
};
</script>

<style lang="scss" scoped>
.gig-card {
  position: relative;
  background: var(--bg-card);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  }

  // 紧急标识
  .urgent-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: linear-gradient(135deg, var(--text-red), #ff6b6b);
    padding: 8rpx 20rpx 8rpx 32rpx;
    border-radius: 0 24rpx 0 24rpx;
    z-index: 2;

    .urgent-text {
      color: white;
      font-size: 24rpx;
      font-weight: 600;
    }

    &::before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 0;
      height: 0;
      border-left: 12rpx solid transparent;
      border-bottom: 12rpx solid rgba(245, 44, 55, 0.8);
    }
  }

  .card-content {
    padding: 32rpx;
  }

  // 头部区域
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32rpx;

    .title-section {
      flex: 1;
      margin-right: 24rpx;

      .gig-title {
        font-size: 36rpx;
        font-weight: 600;
        color: var(--text-base);
        line-height: 1.3;
        margin-bottom: 8rpx;
        display: block;
      }

      .gig-desc {
        font-size: 26rpx;
        color: var(--text-secondary);
        line-height: 1.4;
      }
    }

    .salary-section {
      flex-shrink: 0;
      text-align: right;

      .salary-amount {
        font-size: 40rpx;
        font-weight: 700;
        color: var(--primary);
      }
    }
  }

  // 关键信息网格
  .key-info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24rpx 16rpx;
    margin-bottom: 32rpx;

    .info-item {
      display: flex;
      align-items: flex-start;
      gap: 12rpx;

      .info-icon {
        font-size: 28rpx;
        color: var(--primary);
        margin-top: 2rpx;
        flex-shrink: 0;
      }

      .info-content {
        flex: 1;
        min-width: 0;

        .info-label {
          font-size: 22rpx;
          color: var(--text-info);
          margin-bottom: 2rpx;
          display: block;
        }

        .info-value {
          font-size: 26rpx;
          color: var(--text-base);
          font-weight: 500;
          word-break: break-all;
        }
      }
    }
  }

  // 标签区域
  .tags-section {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-bottom: 32rpx;

    .tag {
      background: var(--bg-tag);
      color: var(--text-secondary);
      padding: 6rpx 12rpx;
      border-radius: 16rpx;
      font-size: 22rpx;
      font-weight: 500;

      &.tag-requirement {
        background: var(--bg-primary-light);
        color: var(--primary);
      }
    }
  }

  // 底部区域
  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16rpx;

    .footer-info {
      flex: 1;
      min-width: 0;

      .publish-time {
        font-size: 22rpx;
        color: var(--text-info);
        margin-bottom: 4rpx;
        display: block;
      }

      .apply-count {
        font-size: 24rpx;
        color: var(--text-secondary);
        font-weight: 500;
      }
    }

    .action-section {
      flex-shrink: 0;

      .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 22rpx;
        font-weight: 600;

        .status-text {
          color: inherit;
        }

        &.status-pending {
          background: var(--bg-warning-light);
          color: var(--text-yellow);
        }

        &.status-confirmed {
          background: var(--bg-success-light);
          color: var(--text-green);
        }

        &.status-rejected {
          background: var(--bg-danger-light);
          color: var(--text-red);
        }
      }

      .apply-button {
        background: linear-gradient(135deg, var(--primary), #ff8533);
        color: white;
        border: none;
        border-radius: 32rpx;
        padding: 16rpx 32rpx;
        font-size: 26rpx;
        font-weight: 600;
        box-shadow: 0 4rpx 16rpx rgba(255, 109, 0, 0.3);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.98);
          box-shadow: 0 2rpx 8rpx rgba(255, 109, 0, 0.4);
        }

        .apply-text {
          color: white;
        }
      }

      .detail-button {
        background: var(--bg-tag);
        color: var(--text-base);
        border: none;
        border-radius: 32rpx;
        padding: 16rpx 32rpx;
        font-size: 26rpx;
        font-weight: 500;
        transition: all 0.3s ease;

        &:active {
          background: var(--text-info);

          .detail-text {
            color: white;
          }
        }

        .detail-text {
          color: var(--text-base);
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 750rpx) {
    .key-info-grid {
      grid-template-columns: 1fr;
      gap: 20rpx;
    }

    .card-footer {
      flex-direction: column;
      align-items: stretch;
      gap: 20rpx;

      .action-section {
        align-self: stretch;

        .apply-button,
        .detail-button {
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}
</style>
