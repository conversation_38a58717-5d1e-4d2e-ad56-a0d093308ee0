<template>
    <view class="experience-selector-wrap">
        <tui-form-item label="经验要求" :borderBottom="false">
            <view class="experience-options flex flex-wrap">
                <view v-for="item in experienceOptions" :key="item.id" class="tag mb-20rpx mr-20rpx"
                    :class="{ 'active-option': modelValue === item.id }" @tap="selectExperience(item)">
                    {{ item.name }}
                </view>
            </view>
        </tui-form-item>
    </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { GIG_EXPERIENCE_OPTIONS } from '@/constants/common'

interface ExperienceOption {
    id: number
    name: string
}

defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
})

const emit = defineEmits(['update:modelValue', 'update:text'])

const experienceOptions = ref(GIG_EXPERIENCE_OPTIONS)

const selectExperience = (experience: ExperienceOption) => {
    emit('update:modelValue', experience.id)
    emit('update:text', experience.name)
}
</script>

<style lang="scss" scoped>
.tag {
    padding: 10rpx 30rpx;
    background-color: #f5f5f5;
    border-radius: 30rpx;
    font-size: 26rpx;
    transition: all 0.2s;
}

.active-option {
    background-color: #ffefe6;
    color: #ff6d00;
    font-weight: bold;
}
</style>