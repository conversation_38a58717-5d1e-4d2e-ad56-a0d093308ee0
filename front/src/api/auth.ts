import { http } from '@/utils/request'

/**
 * 认证相关 API
 * 使用统一的 request 实例，遵循 GET/POST 规范
 */

/**
 * 获取或生成设备ID
 * @returns 设备唯一标识
 */
export const getDeviceId = (): string => {
  let deviceId = uni.getStorageSync('device_id')
  
  if (!deviceId) {
    // 生成设备ID：时间戳 + 随机数 + 设备信息
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    const systemInfo = uni.getSystemInfoSync()
    const deviceInfo = `${systemInfo.platform}_${systemInfo.model}`.replace(/\s+/g, '_')
    
    deviceId = `${timestamp}_${random}_${deviceInfo}`.substring(0, 32)
    
    // 持久化存储
    uni.setStorageSync('device_id', deviceId)
  }
  
  return deviceId
}

/**
 * 重置设备ID（用于特殊情况）
 * @returns 新的设备ID
 */
export const resetDeviceId = (): string => {
  uni.removeStorageSync('device_id')
  return getDeviceId()
}

/**
 * 微信小程序登录
 * @param data 登录数据
 * @returns 登录结果
 */
export const wechatLogin = (data: { loginCode: string; phoneCode?: string; deviceId?: string }) => {
  const deviceId = data.deviceId || getDeviceId()
  return http.post("/auth/wechat-login", {
    loginCode: data.loginCode,
    phoneCode: data.phoneCode,
    deviceId
  })
}

/**
 * 检查用户是否注册（根据手机号）
 * @param data 手机号
 * @returns 检查结果
 */
export const checkUser = (data: { phone: string }) => {
  return http.post("/auth/check-user", data)
}

/**
 * 检查用户是否注册（根据微信code）
 * @param data 微信code
 * @returns 检查结果
 */
export const checkUserByCode = (data: { code: string; deviceId?: string }) => {
  const deviceId = data.deviceId || getDeviceId()
  return http.post("/auth/check-user-by-code", {
    code: data.code,
    deviceId
  })
}

/**
 * 获取用户信息
 * @returns 用户信息
 */
export const getUserInfo = () => {
  return http.get("/auth/userinfo")
}

/**
 * 刷新Token
 * @param refreshToken 刷新令牌
 * @returns 新的Token
 */
export const refreshToken = (refreshToken: string) => {
  return http.post("/auth/refresh", { refreshToken })
}

/**
 * 退出登录
 * @returns 退出结果
 */
export const logout = () => {
  return http.post("/auth/logout", {})
}

/**
 * 绑定手机号
 * @param data 手机号绑定数据
 * @returns 绑定结果
 */
export const bindPhone = (data: { phoneCode: string }) => {
  return http.post("/users/bind-phone", data)
} 