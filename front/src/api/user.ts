import { http } from "@/utils/request";
import type { UserInfo } from "@/types/user";

/**
 * 获取当前登录用户信息
 * @returns {Promise<ResponseData<UserInfo>>}
 */
export const getUserProfile = () => {
    return http.get<UserInfo>("/users/profile");
};

/**
 * 更新用户信息
 * @param {Partial<UserInfo>} data
 * @returns {Promise<ResponseData<any>>}
 */
export const updateUserProfile = (data: Partial<UserInfo>) => {
    return http.post("/users/profile", data);
}; 