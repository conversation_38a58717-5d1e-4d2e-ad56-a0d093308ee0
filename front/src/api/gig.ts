import { http } from "@/utils/request";
import type {
  Gig,
  CreateGigRequest,
  ListGigsRequest,
  ListGigsResponse,
  ApplyGigRequest,
  UpdateApplicationStatusRequest,
  ListApplicationsRequest,
  MonthlyStatsRequest,
  MonthlyStatsResponse,
  DailyGigRequest,
  DailyGig,
  CheckApplicationStatusResponse,
} from '@/types/gig';
import type { PaginatedRequest } from '@/types/common';

/**
 * 发布一个新零工
 * @param data - 零工的详细信息 (前端 snake_case 模型)
 * @returns 创建的零工对象
 */
export const createGig = (data: CreateGigRequest) => {
  // 数据格式已在UI层处理完毕，直接发送
  return http.post<Gig>('/gigs', data);
};

/**
 * 获取公开的零工列表 (支持筛选、分页)
 * @param params - 查询参数 (前端 snake_case 模型)
 * @returns 分页的零工列表
 */
export const listGigs = (params: ListGigsRequest) => {
  return http.get<ListGigsResponse>('/gigs', { params });
};

/**
 * 获取我发布的用于管理的零工列表
 * @param params 分页等查询参数
 * @returns 分页的零工列表
 */
export const listMyGigs = (params: PaginatedRequest) => {
  return http.get<ListGigsResponse>('/gigs/manage', { params });
};

/**
 * 获取单个零工的详细信息
 * @param id 零工的 ID
 * @returns 零工的详细信息
 */
export const getGig = (id: number) => {
  return http.get<Gig>(`/gigs/${id}`);
};

/**
 * 删除一个零工 (使用 POST)
 * @param id 要删除的零工的 ID
 */
export const deleteGig = (id: number) => {
  return http.post(`/gigs/${id}/delete`);
};

/**
 * 申请一个零工
 * @param data 申请信息
 */
export const applyForGig = (data: ApplyGigRequest) => {
  return http.post('/gigs/apply', data);
};

/**
 * 获取零工的申请列表
 * @param gigId 零工ID
 * @param params 分页参数
 */
export const listGigApplications = (gigId: number, params: PaginatedRequest) => {
  return http.get(`/gigs/${gigId}/applications`, { params });
};

/**
 * 获取我（用户）的申请列表
 * @param params 分页参数
 */
export const listMyApplications = (params: ListApplicationsRequest) => {
  return http.get('/gigs/applications/my', { params });
};

/**
 * 审核一个申请 (招聘者操作)
 * @param data 审核信息
 */
export const reviewApplication = (data: UpdateApplicationStatusRequest) => {
  return http.post('/gigs/applications/review', data);
};

/**
 * 获取月度统计数据
 * @param year 年份
 * @param month 月份
 */
export const getMonthlyStats = (year: number, month: number) => {
  return http.get<MonthlyStatsResponse>('/gigs/calendar/monthly-stats', { 
    params: { year, month } 
  });
};

/**
 * 获取每日零工数据
 * @param date 日期 (YYYY-MM-DD格式)
 */
export const getDailyGigs = (date: string) => {
  return http.get<DailyGig>('/gigs/calendar/daily-gigs', { 
    params: { date } 
  });
};

/**
 * 检查用户是否已申请某个零工
 * @param gigId 零工ID
 */
export const checkApplicationStatus = (gigId: number) => {
  return http.get<CheckApplicationStatusResponse>(`/gigs/${gigId}/application-status`);
};