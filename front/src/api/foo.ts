import { http } from "@/utils/request";
import type { UploadTokenResponse } from "@/types/common";

/**
 * 获取七牛云上传凭证
 * @returns {Promise<ResponseData<UploadTokenResponse>>}
 */
export const getUploadToken = () => {
  return http.post<UploadTokenResponse>("/common/upload-token");
};

/**
 * 文件上传
 * @param file 文件数据
 * @returns 上传结果
 */
export const uploadFile = (file: any) => {
  return http.post("/common/upload", file);
};

/**
 * 获取配置信息
 * @returns 配置信息
 */
export const getConfig = () => {
  return http.get("/common/config");
};


