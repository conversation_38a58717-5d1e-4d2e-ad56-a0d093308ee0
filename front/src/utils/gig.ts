/**
 * 零工工具函数
 * 仅包含纯工具函数，不包含业务逻辑
 */
import { GIG_SALARY_UNIT_MAP, GigStatus, GigApplicationStatus, getGigStatusDetails, getGigApplicationStatusDetails } from '@/constants/gig';
import { getGenderText, getExperienceText } from '@/utils/standards';
import type { Gig } from '@/types/gig';
import dayjs from 'dayjs';

// ====================================================================
// 薪资格式化函数
// ====================================================================

/**
 * 格式化薪资显示
 * @param salary 薪资（分）
 * @param salary_unit 薪资单位
 */
export function formatSalary(salary: number, salary_unit: number): string {
    const yuan = (salary / 100).toFixed(2);
    const unit = GIG_SALARY_UNIT_MAP[salary_unit] || '次';
    return `¥${yuan}/${unit}`;
}

/**
 * 格式化薪资金额（不包含单位）
 * @param salary 薪资（分）
 */
export function formatSalaryAmount(salary: number): string {
    return (salary / 100).toFixed(2);
}

/**
 * 获取薪资单位文本
 * @param salary_unit 薪资单位
 */
export function getSalaryUnitText(salary_unit: number): string {
    return GIG_SALARY_UNIT_MAP[salary_unit] || '次';
}

// ====================================================================
// 状态文本和样式函数
// ====================================================================

/**
 * 获取零工状态文本
 * @param status 状态值
 */
export function getGigStatusText(status: GigStatus): string {
    return getGigStatusDetails(status).text;
}

/**
 * 获取零工状态样式类名
 * @param status 状态值
 */
export function getGigStatusClass(status: GigStatus): string {
    return getGigStatusDetails(status).class;
}

/**
 * 获取申请状态文本
 * @param status 状态值
 * @param role 角色：'seeker' 或 'recruiter'
 */
export function getApplicationStatusText(status: GigApplicationStatus, role: 'seeker' | 'recruiter' = 'seeker'): string {
    return getGigApplicationStatusDetails(status, role).text;
}

/**
 * 获取申请状态样式类名
 * @param status 状态值
 * @param role 角色：'seeker' 或 'recruiter'
 */
export function getApplicationStatusClass(status: GigApplicationStatus, role: 'seeker' | 'recruiter' = 'seeker'): string {
    return getGigApplicationStatusDetails(status, role).class;
}

// ====================================================================
// 地址格式化函数
// ====================================================================

/**
 * 格式化零工完整地址
 * @param gig 零工信息
 */
export function formatGigAddress(gig: Gig): string {
    if (gig.detail_address) {
        return `${gig.address_name} ${gig.detail_address}`;
    }
    return gig.address_name;
}

// ====================================================================
// 招聘进度格式化函数
// ====================================================================

/**
 * 获取零工招聘进度文本
 * @param gig 零工信息
 */
export function getGigRecruitmentProgress(gig: Gig): string {
    return `${gig.current_people_count}/${gig.people_count}`;
}

/**
 * 计算零工招聘进度百分比
 * @param gig 零工信息
 */
export function getGigRecruitmentPercentage(gig: Gig): number {
    if (gig.people_count === 0) return 0;
    return Math.round((gig.current_people_count / gig.people_count) * 100);
}

/**
 * 获取零工剩余招聘人数
 * @param gig 零工信息
 */
export function getGigRemainingSlots(gig: Gig): number {
    return Math.max(0, gig.people_count - gig.current_people_count);
}

// ====================================================================
// 时间格式化函数
// ====================================================================

/**
 * 格式化零工创建时间
 * @param created_at 创建时间
 */
export function formatGigCreatedTime(created_at: string): string {
    const now = dayjs();
    const created = dayjs(created_at);
    const diffHours = now.diff(created, 'hour');
    
    if (diffHours < 1) {
        return '刚刚发布';
    } else if (diffHours < 24) {
        return `${diffHours}小时前`;
    } else {
        const diffDays = now.diff(created, 'day');
        if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return created.format('MM月DD日');
        }
    }
}

// ====================================================================
// 紧急程度计算函数
// ====================================================================

/**
 * 计算零工的紧急程度
 * @param gig 零工信息
 */
export function getUrgencyLevel(gig: Gig): 'high' | 'medium' | 'low' {
    const now = new Date();
    const startTime = new Date(gig.start_time);
    const diffHours = (startTime.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (diffHours <= 24) return 'high';
    if (diffHours <= 72) return 'medium';
    return 'low';
}

/**
 * 检查零工是否即将开始（24小时内）
 * @param gig 零工信息
 */
export function isGigStartingSoon(gig: Gig): boolean {
    return getUrgencyLevel(gig) === 'high';
}

/**
 * 检查零工是否过期
 * @param gig 零工信息
 */
export function isGigExpired(gig: Gig): boolean {
    return dayjs().isAfter(dayjs(gig.end_time));
}

/**
 * 检查零工是否招满
 * @param gig 零工信息
 */
export function isGigFull(gig: Gig): boolean {
    return gig.current_people_count >= gig.people_count;
}

// ====================================================================
// 文本工具函数
// ====================================================================

// getGenderText 和 getExperienceText 已从 @/utils/standards 中导入
export { getGenderText, getExperienceText }; 