/**
 * 表单验证工具
 * 提供统一的表单验证规则和验证方法
 */

// ====================================================================
// 类型定义
// ====================================================================

export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: RegExp;
  validator?: (value: any) => boolean | string;
  message?: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  fieldErrors: Record<string, string>;
}

export interface FormField {
  name: string;
  label: string;
  value: any;
  rules: ValidationRule[];
}

// ====================================================================
// 基础验证函数
// ====================================================================

/**
 * 验证必填项
 * @param value 字段值
 * @param message 错误消息
 */
export function validateRequired(value: any, message: string = '此项为必填项'): boolean | string {
  if (value === null || value === undefined || value === '') {
    return message;
  }
  if (Array.isArray(value) && value.length === 0) {
    return message;
  }
  return true;
}

/**
 * 验证字符串长度
 * @param value 字符串值
 * @param min 最小长度
 * @param max 最大长度
 * @param message 错误消息
 */
export function validateLength(
  value: string,
  min?: number,
  max?: number,
  message?: string
): boolean | string {
  if (!value) return true; // 空值跳过长度验证
  
  const length = value.length;
  
  if (min !== undefined && length < min) {
    return message || `长度不能少于${min}个字符`;
  }
  
  if (max !== undefined && length > max) {
    return message || `长度不能超过${max}个字符`;
  }
  
  return true;
}

/**
 * 验证数值范围
 * @param value 数值
 * @param min 最小值
 * @param max 最大值
 * @param message 错误消息
 */
export function validateRange(
  value: number,
  min?: number,
  max?: number,
  message?: string
): boolean | string {
  if (value === null || value === undefined) return true; // 空值跳过范围验证
  
  if (min !== undefined && value < min) {
    return message || `值不能小于${min}`;
  }
  
  if (max !== undefined && value > max) {
    return message || `值不能大于${max}`;
  }
  
  return true;
}

/**
 * 验证正则表达式
 * @param value 字符串值
 * @param pattern 正则表达式
 * @param message 错误消息
 */
export function validatePattern(
  value: string,
  pattern: RegExp,
  message: string = '格式不正确'
): boolean | string {
  if (!value) return true; // 空值跳过正则验证
  
  if (!pattern.test(value)) {
    return message;
  }
  
  return true;
}

// ====================================================================
// 常用验证规则
// ====================================================================

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 * @param message 错误消息
 */
export function validateEmail(email: string, message: string = '邮箱格式不正确'): boolean | string {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return validatePattern(email, emailRegex, message);
}

/**
 * 验证手机号格式
 * @param phone 手机号
 * @param message 错误消息
 */
export function validatePhone(phone: string, message: string = '手机号格式不正确'): boolean | string {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return validatePattern(phone, phoneRegex, message);
}

/**
 * 验证身份证号格式
 * @param idCard 身份证号
 * @param message 错误消息
 */
export function validateIdCard(idCard: string, message: string = '身份证号格式不正确'): boolean | string {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return validatePattern(idCard, idCardRegex, message);
}

/**
 * 验证密码强度
 * @param password 密码
 * @param minLength 最小长度
 * @param requireSpecial 是否需要特殊字符
 * @param message 错误消息
 */
export function validatePassword(
  password: string,
  minLength: number = 6,
  requireSpecial: boolean = false,
  message?: string
): boolean | string {
  if (!password) return true; // 空值跳过密码验证
  
  if (password.length < minLength) {
    return message || `密码长度不能少于${minLength}位`;
  }
  
  if (requireSpecial) {
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    
    if (!hasLetter || !hasNumber || !hasSpecial) {
      return message || '密码必须包含字母、数字和特殊字符';
    }
  }
  
  return true;
}

/**
 * 验证确认密码
 * @param password 原密码
 * @param confirmPassword 确认密码
 * @param message 错误消息
 */
export function validateConfirmPassword(
  password: string,
  confirmPassword: string,
  message: string = '两次输入的密码不一致'
): boolean | string {
  if (password !== confirmPassword) {
    return message;
  }
  return true;
}

/**
 * 验证中文姓名
 * @param name 姓名
 * @param message 错误消息
 */
export function validateChineseName(name: string, message: string = '请输入正确的中文姓名'): boolean | string {
  const nameRegex = /^[\u4e00-\u9fa5]{2,8}$/;
  return validatePattern(name, nameRegex, message);
}

/**
 * 验证网址
 * @param url 网址
 * @param message 错误消息
 */
export function validateUrl(url: string, message: string = '网址格式不正确'): boolean | string {
  const urlRegex = /^https?:\/\/[^\s]+$/;
  return validatePattern(url, urlRegex, message);
}

// ====================================================================
// 业务相关验证
// ====================================================================

/**
 * 验证价格
 * @param price 价格
 * @param min 最小价格
 * @param max 最大价格
 * @param message 错误消息
 */
export function validatePrice(
  price: number,
  min: number = 0,
  max: number = 999999,
  message?: string
): boolean | string {
  if (price === null || price === undefined) {
    return '请输入价格';
  }
  
  if (price < min) {
    return message || `价格不能低于${min}元`;
  }
  
  if (price > max) {
    return message || `价格不能超过${max}元`;
  }
  
  return true;
}

/**
 * 验证工作时间
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param message 错误消息
 */
export function validateWorkTime(
  startTime: string,
  endTime: string,
  message: string = '结束时间必须晚于开始时间'
): boolean | string {
  if (!startTime || !endTime) return true; // 空值跳过时间验证
  
  const start = new Date(startTime);
  const end = new Date(endTime);
  
  if (end <= start) {
    return message;
  }
  
  return true;
}

/**
 * 验证年龄
 * @param age 年龄
 * @param min 最小年龄
 * @param max 最大年龄
 * @param message 错误消息
 */
export function validateAge(
  age: number,
  min: number = 16,
  max: number = 65,
  message?: string
): boolean | string {
  if (age === null || age === undefined) return true; // 空值跳过年龄验证
  
  if (age < min) {
    return message || `年龄不能小于${min}岁`;
  }
  
  if (age > max) {
    return message || `年龄不能大于${max}岁`;
  }
  
  return true;
}

// ====================================================================
// 表单验证器
// ====================================================================

/**
 * 表单验证器类
 */
export class FormValidator {
  private fields: FormField[] = [];
  
  /**
   * 添加字段
   * @param field 字段配置
   */
  addField(field: FormField): FormValidator {
    this.fields.push(field);
    return this;
  }
  
  /**
   * 验证单个字段
   * @param field 字段配置
   */
  validateField(field: FormField): string | null {
    const { value, rules } = field;
    
    for (const rule of rules) {
      // 必填验证
      if (rule.required) {
        const result = validateRequired(value, rule.message);
        if (result !== true) {
          return result as string;
        }
      }
      
      // 长度验证
      if (rule.min !== undefined || rule.max !== undefined) {
        const result = validateLength(value, rule.min, rule.max, rule.message);
        if (result !== true) {
          return result as string;
        }
      }
      
      // 正则验证
      if (rule.pattern) {
        const result = validatePattern(value, rule.pattern, rule.message);
        if (result !== true) {
          return result as string;
        }
      }
      
      // 自定义验证
      if (rule.validator) {
        const result = rule.validator(value);
        if (result !== true) {
          return result as string;
        }
      }
    }
    
    return null;
  }
  
  /**
   * 验证所有字段
   */
  validate(): ValidationResult {
    const errors: string[] = [];
    const fieldErrors: Record<string, string> = {};
    
    for (const field of this.fields) {
      const error = this.validateField(field);
      if (error) {
        errors.push(`${field.label}: ${error}`);
        fieldErrors[field.name] = error;
      }
    }
    
    return {
      valid: errors.length === 0,
      errors,
      fieldErrors,
    };
  }
  
  /**
   * 清空所有字段
   */
  clear(): FormValidator {
    this.fields = [];
    return this;
  }
}

/**
 * 创建表单验证器
 */
export function createFormValidator(): FormValidator {
  return new FormValidator();
}

// ====================================================================
// 快速验证函数
// ====================================================================

/**
 * 快速验证表单数据
 * @param data 表单数据
 * @param rules 验证规则
 */
export function validateForm(
  data: Record<string, any>,
  rules: Record<string, ValidationRule[]>
): ValidationResult {
  const validator = createFormValidator();
  
  for (const [fieldName, fieldRules] of Object.entries(rules)) {
    validator.addField({
      name: fieldName,
      label: fieldName,
      value: data[fieldName],
      rules: fieldRules,
    });
  }
  
  return validator.validate();
}

// ====================================================================
// 预设验证规则
// ====================================================================

/**
 * 零工发布表单验证规则
 */
export const gigPublishRules = {
  title: [
    { required: true, message: '请输入零工标题' },
    { min: 5, max: 50, message: '标题长度应在5-50字符之间' }
  ],
  description: [
    { required: true, message: '请输入零工描述' },
    { min: 10, max: 500, message: '描述长度应在10-500字符之间' }
  ],
  salary: [
    { required: true, message: '请输入薪资' },
    { validator: (value: number) => validatePrice(value, 1, 99999) }
  ],
  people_count: [
    { required: true, message: '请输入招聘人数' },
    { validator: (value: number) => validateRange(value, 1, 999, '招聘人数应在1-999之间') }
  ],
  start_time: [
    { required: true, message: '请选择开始时间' }
  ],
  end_time: [
    { required: true, message: '请选择结束时间' }
  ],
  address_name: [
    { required: true, message: '请输入工作地点' }
  ],
  contact_name: [
    { required: true, message: '请输入联系人姓名' },
    { validator: (value: string) => validateChineseName(value) }
  ],
  contact_phone: [
    { required: true, message: '请输入联系电话' },
    { validator: (value: string) => validatePhone(value) }
  ],
};

/**
 * 用户注册表单验证规则
 */
export const userRegisterRules = {
  nickname: [
    { required: true, message: '请输入昵称' },
    { min: 2, max: 20, message: '昵称长度应在2-20字符之间' }
  ],
  phone: [
    { required: true, message: '请输入手机号' },
    { validator: (value: string) => validatePhone(value) }
  ],
  password: [
    { required: true, message: '请输入密码' },
    { validator: (value: string) => validatePassword(value, 6, true) }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码' }
  ],
  email: [
    { validator: (value: string) => !value || validateEmail(value) }
  ],
};

/**
 * 个人信息表单验证规则
 */
export const profileRules = {
  nickname: [
    { required: true, message: '请输入昵称' },
    { min: 2, max: 20, message: '昵称长度应在2-20字符之间' }
  ],
  realName: [
    { validator: (value: string) => !value || validateChineseName(value) }
  ],
  phone: [
    { required: true, message: '请输入手机号' },
    { validator: (value: string) => validatePhone(value) }
  ],
  email: [
    { validator: (value: string) => !value || validateEmail(value) }
  ],
  age: [
    { validator: (value: number) => !value || validateAge(value) }
  ],
};