/**
 * API 调用辅助工具
 * 提供统一的 API 调用处理、错误处理、加载状态管理等功能
 */

import { showLoading, hideLoading, showErrorToast, showSuccessToast } from '@/utils/ui';

// ====================================================================
// 类型定义
// ====================================================================

export interface ApiOptions {
  showLoading?: boolean;
  loadingText?: string;
  showSuccessToast?: boolean;
  successText?: string;
  showErrorToast?: boolean;
  errorText?: string;
  retryCount?: number;
  timeout?: number;
}

export interface ApiResult<T = any> {
  success: boolean;
  data?: T;
  error?: any;
  message?: string;
}

// ====================================================================
// 核心 API 调用函数
// ====================================================================

/**
 * 带有完整错误处理和状态管理的 API 调用包装器
 * @param apiFunction API 调用函数
 * @param options 配置选项
 */
export async function callApi<T = any>(
  apiFunction: () => Promise<T>,
  options: ApiOptions = {}
): Promise<ApiResult<T>> {
  const {
    showLoading: shouldShowLoading = false,
    loadingText = '加载中...',
    showSuccessToast: shouldShowSuccessToast = false,
    successText = '操作成功',
    showErrorToast: shouldShowErrorToast = true,
    errorText = '操作失败',
    retryCount = 0,
    timeout = 30000,
  } = options;

  let attempt = 0;
  let lastError: any;

  // 显示加载状态
  if (shouldShowLoading) {
    showLoading(loadingText);
  }

  try {
    while (attempt <= retryCount) {
      try {
        // 设置超时
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('请求超时')), timeout);
        });

        const result = await Promise.race([
          apiFunction(),
          timeoutPromise,
        ]);

        // 成功时显示提示
        if (shouldShowSuccessToast) {
          showSuccessToast(successText);
        }

        return {
          success: true,
          data: result,
        };
      } catch (error) {
        lastError = error;
        attempt++;
        
        // 如果还有重试机会，等待一段时间后重试
        if (attempt <= retryCount) {
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    // 所有重试都失败了
    throw lastError;
  } catch (error) {
    const errorMessage = getErrorMessage(error, errorText);
    
    // 显示错误提示
    if (shouldShowErrorToast) {
      showErrorToast(errorMessage);
    }

    console.error('API 调用失败:', error);

    return {
      success: false,
      error,
      message: errorMessage,
    };
  } finally {
    // 隐藏加载状态
    if (shouldShowLoading) {
      hideLoading();
    }
  }
}

/**
 * 简单的 API 调用（只处理错误，不显示加载状态）
 * @param apiFunction API 调用函数
 * @param errorText 错误提示文本
 */
export async function simpleApiCall<T = any>(
  apiFunction: () => Promise<T>,
  errorText: string = '操作失败'
): Promise<T | null> {
  try {
    return await apiFunction();
  } catch (error) {
    const errorMessage = getErrorMessage(error, errorText);
    showErrorToast(errorMessage);
    console.error('API 调用失败:', error);
    return null;
  }
}

/**
 * 带加载状态的 API 调用
 * @param apiFunction API 调用函数
 * @param loadingText 加载提示文本
 * @param successText 成功提示文本（可选）
 * @deprecated Use withLoading from ui.ts for simple loading states, or callApi with showLoading option for complex scenarios
 */
export async function apiCallWithLoading<T = any>(
  apiFunction: () => Promise<T>,
  loadingText: string = '加载中...',
  successText?: string
): Promise<T | null> {
  const result = await callApi(apiFunction, {
    showLoading: true,
    loadingText,
    showSuccessToast: !!successText,
    successText,
  });

  return result.success ? result.data! : null;
}

/**
 * 提交表单数据的 API 调用
 * @param apiFunction API 调用函数
 * @param successText 成功提示文本
 * @param successCallback 成功回调
 */
export async function submitForm<T = any>(
  apiFunction: () => Promise<T>,
  successText: string = '提交成功',
  successCallback?: (data: T) => void
): Promise<boolean> {
  const result = await callApi(apiFunction, {
    showLoading: true,
    loadingText: '提交中...',
    showSuccessToast: true,
    successText,
  });

  if (result.success && successCallback) {
    successCallback(result.data!);
  }

  return result.success;
}

/**
 * 删除操作的 API 调用
 * @param apiFunction API 调用函数
 * @param itemName 删除项目名称
 * @param successCallback 成功回调
 */
export async function deleteItem<T = any>(
  apiFunction: () => Promise<T>,
  itemName: string = '项目',
  successCallback?: () => void
): Promise<boolean> {
  const result = await callApi(apiFunction, {
    showLoading: true,
    loadingText: '删除中...',
    showSuccessToast: true,
    successText: `${itemName}删除成功`,
  });

  if (result.success && successCallback) {
    successCallback();
  }

  return result.success;
}

// ====================================================================
// 错误处理函数
// ====================================================================

/**
 * 获取错误消息
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 */
export function getErrorMessage(error: any, defaultMessage: string = '操作失败'): string {
  if (!error) return defaultMessage;

  // 如果是字符串，直接返回
  if (typeof error === 'string') {
    return error;
  }

  // 处理常见的错误格式
  if (error.response) {
    // HTTP 错误响应
    const { status, data } = error.response;
    
    if (data?.message) {
      return data.message;
    }
    
    if (data?.error) {
      return data.error;
    }
    
    // 根据 HTTP 状态码返回相应的错误消息
    switch (status) {
      case 400:
        return '请求参数错误';
      case 401:
        return '请先登录';
      case 403:
        return '没有权限执行此操作';
      case 404:
        return '请求的资源不存在';
      case 500:
        return '服务器内部错误';
      case 502:
        return '网关错误';
      case 503:
        return '服务不可用';
      case 504:
        return '请求超时';
      default:
        return `请求失败 (${status})`;
    }
  }

  // 处理网络错误
  if (error.message) {
    if (error.message.includes('Network Error')) {
      return '网络连接失败，请检查网络设置';
    }
    if (error.message.includes('timeout')) {
      return '请求超时，请稍后重试';
    }
    return error.message;
  }

  // 处理 UniApp 错误
  if (error.errMsg) {
    return error.errMsg;
  }

  return defaultMessage;
}

// ====================================================================
// 特殊业务场景的 API 调用
// ====================================================================

/**
 * 获取数据列表的 API 调用
 * @param apiFunction API 调用函数
 * @param emptyText 空数据提示文本
 */
export async function fetchList<T = any>(
  apiFunction: () => Promise<T[]>,
  emptyText: string = '暂无数据'
): Promise<T[]> {
  const result = await callApi(apiFunction, {
    showLoading: true,
    loadingText: '获取数据中...',
    showErrorToast: true,
    errorText: '获取数据失败',
  });

  if (result.success) {
    const data = result.data || [];
    if (data.length === 0) {
      // 可以在这里显示空数据提示
      console.info(emptyText);
    }
    return data;
  }

  return [];
}

/**
 * 获取单个数据的 API 调用
 * @param apiFunction API 调用函数
 * @param notFoundText 数据不存在提示文本
 */
export async function fetchSingle<T = any>(
  apiFunction: () => Promise<T>,
  notFoundText: string = '数据不存在'
): Promise<T | null> {
  const result = await callApi(apiFunction, {
    showLoading: true,
    loadingText: '获取数据中...',
    showErrorToast: true,
    errorText: notFoundText,
  });

  return result.success ? result.data! : null;
}

/**
 * 批量操作的 API 调用
 * @param apiFunction API 调用函数
 * @param itemCount 操作项目数量
 * @param operationName 操作名称
 */
export async function batchOperation<T = any>(
  apiFunction: () => Promise<T>,
  itemCount: number,
  operationName: string = '处理'
): Promise<boolean> {
  const result = await callApi(apiFunction, {
    showLoading: true,
    loadingText: `${operationName}中...`,
    showSuccessToast: true,
    successText: `成功${operationName}${itemCount}个项目`,
  });

  return result.success;
}

// ====================================================================
// 缓存相关函数
// ====================================================================

interface CacheItem<T> {
  data: T;
  timestamp: number;
  expireTime: number;
}

const cache = new Map<string, CacheItem<any>>();

/**
 * 带缓存的 API 调用
 * @param key 缓存键
 * @param apiFunction API 调用函数
 * @param expireTime 过期时间（毫秒）
 * @param forceRefresh 是否强制刷新
 */
export async function cachedApiCall<T = any>(
  key: string,
  apiFunction: () => Promise<T>,
  expireTime: number = 5 * 60 * 1000, // 默认5分钟
  forceRefresh: boolean = false
): Promise<T | null> {
  const now = Date.now();
  
  // 检查缓存
  if (!forceRefresh && cache.has(key)) {
    const cached = cache.get(key)!;
    if (now < cached.timestamp + cached.expireTime) {
      return cached.data;
    } else {
      // 缓存过期，删除
      cache.delete(key);
    }
  }

  // 调用 API
  const result = await simpleApiCall(apiFunction);
  
  if (result !== null) {
    // 保存到缓存
    cache.set(key, {
      data: result,
      timestamp: now,
      expireTime,
    });
  }

  return result;
}

/**
 * 清除指定缓存
 * @param key 缓存键
 */
export function clearCache(key: string) {
  cache.delete(key);
}

/**
 * 清除所有缓存
 */
export function clearAllCache() {
  cache.clear();
}

/**
 * 清除过期缓存
 */
export function clearExpiredCache() {
  const now = Date.now();
  for (const [key, item] of cache.entries()) {
    if (now >= item.timestamp + item.expireTime) {
      cache.delete(key);
    }
  }
}