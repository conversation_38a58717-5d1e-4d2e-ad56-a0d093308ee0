/**
 * 字符串处理工具函数
 * 提供各种字符串操作功能
 */

// ====================================================================
// 字符串清理和处理
// ====================================================================

/**
 * 去左右空格
 * @param value 需要处理的字符串
 */
export function trim(value: string): string {
    return value.replace(/(^\s*)|(\s*$)/g, "");
}

/**
 * 去所有空格
 * @param value 需要处理的字符串
 */
export function trimAll(value: string): string {
    return value.replace(/\s+/g, "");
}

/**
 * 替换所有相同字符串
 * @param text 需要处理的字符串
 * @param repstr 被替换的字符
 * @param newstr 替换后的字符
 */
export function replaceAll(text: string, repstr: string, newstr: string): string {
    return text.replace(new RegExp(repstr, "gm"), newstr);
}

/**
 * 移除HTML标签
 * @param html HTML字符串
 */
export function stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '');
}

/**
 * 转义HTML字符
 * @param text 需要转义的文本
 */
export function escapeHtml(text: string): string {
    const map: Record<string, string> = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;'
    };
    return text.replace(/[&<>"']/g, (match) => map[match]);
}

/**
 * 反转义HTML字符
 * @param html 需要反转义的HTML
 */
export function unescapeHtml(html: string): string {
    const map: Record<string, string> = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#39;': "'"
    };
    return html.replace(/&(amp|lt|gt|quot|#39);/g, (match) => map[match]);
}

// ====================================================================
// 字符串格式化
// ====================================================================

/**
 * 格式化手机号码（隐藏中间4位）
 * @param phone 手机号码
 */
export function formatPhoneNumber(phone: string): string {
    if (!phone) return '';
    return phone.length === 11 ? phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2') : phone;
}

/**
 * 格式化身份证号码（隐藏中间部分）
 * @param idCard 身份证号码
 */
export function formatIdCard(idCard: string): string {
    if (!idCard) return '';
    if (idCard.length === 18) {
        return idCard.replace(/^(\d{6})\d{8}(\d{4})$/, '$1********$2');
    } else if (idCard.length === 15) {
        return idCard.replace(/^(\d{6})\d{6}(\d{3})$/, '$1******$2');
    }
    return idCard;
}

/**
 * 格式化银行卡号（隐藏中间部分）
 * @param cardNo 银行卡号
 */
export function formatBankCard(cardNo: string): string {
    if (!cardNo) return '';
    if (cardNo.length >= 12) {
        return cardNo.replace(/^(\d{4})\d*(\d{4})$/, '$1****$2');
    }
    return cardNo;
}

/**
 * 格式化姓名（隐藏中间字符）
 * @param name 姓名
 */
export function formatName(name: string): string {
    if (!name) return '';
    if (name.length <= 2) {
        return name.charAt(0) + '*';
    } else if (name.length === 3) {
        return name.charAt(0) + '*' + name.charAt(2);
    } else {
        return name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1);
    }
}

// ====================================================================
// 字符串验证
// ====================================================================

/**
 * 验证邮箱格式
 * @param email 邮箱地址
 */
export function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * 验证手机号格式
 * @param phone 手机号
 */
export function isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
}

/**
 * 验证身份证格式
 * @param idCard 身份证号
 */
export function isValidIdCard(idCard: string): boolean {
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return idCardRegex.test(idCard);
}

/**
 * 验证密码强度
 * @param password 密码
 */
export function validatePasswordStrength(password: string): {
    isValid: boolean;
    strength: 'weak' | 'medium' | 'strong';
    message: string;
} {
    if (password.length < 6) {
        return { isValid: false, strength: 'weak', message: '密码长度不能少于6位' };
    }
    
    let strength = 0;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    if (strength < 2) {
        return { isValid: false, strength: 'weak', message: '密码强度太弱' };
    } else if (strength < 3) {
        return { isValid: true, strength: 'medium', message: '密码强度中等' };
    } else {
        return { isValid: true, strength: 'strong', message: '密码强度良好' };
    }
}

// ====================================================================
// 字符串转换
// ====================================================================

/**
 * 首字母大写
 * @param str 字符串
 */
export function capitalize(str: string): string {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * 驼峰命名转换
 * @param str 字符串
 */
export function toCamelCase(str: string): string {
    return str.replace(/[-_\s]+(.)?/g, (_, char) => char ? char.toUpperCase() : '');
}

/**
 * 下划线命名转换
 * @param str 字符串
 */
export function toSnakeCase(str: string): string {
    return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`);
}

/**
 * 短横线命名转换
 * @param str 字符串
 */
export function toKebabCase(str: string): string {
    return str.replace(/[A-Z]/g, (letter) => `-${letter.toLowerCase()}`);
}

/**
 * 标题格式转换（每个单词首字母大写）
 * @param str 字符串
 */
export function toTitleCase(str: string): string {
    return str.replace(/\w\S*/g, (txt) => 
        txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
}

// ====================================================================
// 字符串分析
// ====================================================================

/**
 * 计算字符串字节长度（中文2字节，英文1字节）
 * @param str 字符串
 */
export function getByteLength(str: string): number {
    let length = 0;
    for (let i = 0; i < str.length; i++) {
        const code = str.charCodeAt(i);
        if (code > 255) {
            length += 2;
        } else {
            length += 1;
        }
    }
    return length;
}

/**
 * 统计字符串中的字符数量
 * @param str 字符串
 */
export function getCharacterCount(str: string): {
    total: number;
    chinese: number;
    english: number;
    number: number;
    space: number;
    special: number;
} {
    const result = {
        total: str.length,
        chinese: 0,
        english: 0,
        number: 0,
        space: 0,
        special: 0
    };
    
    for (let i = 0; i < str.length; i++) {
        const char = str.charAt(i);
        const code = str.charCodeAt(i);
        
        if (char === ' ') {
            result.space++;
        } else if (code > 255) {
            result.chinese++;
        } else if (/[a-zA-Z]/.test(char)) {
            result.english++;
        } else if (/[0-9]/.test(char)) {
            result.number++;
        } else {
            result.special++;
        }
    }
    
    return result;
}

/**
 * 检查字符串是否包含中文
 * @param str 字符串
 */
export function hasChinese(str: string): boolean {
    return /[\u4e00-\u9fa5]/.test(str);
}

/**
 * 检查字符串是否为纯中文
 * @param str 字符串
 */
export function isAllChinese(str: string): boolean {
    return /^[\u4e00-\u9fa5]+$/.test(str);
}

/**
 * 检查字符串是否为纯英文
 * @param str 字符串
 */
export function isAllEnglish(str: string): boolean {
    return /^[a-zA-Z]+$/.test(str);
}

/**
 * 检查字符串是否为纯数字
 * @param str 字符串
 */
export function isAllNumber(str: string): boolean {
    return /^[0-9]+$/.test(str);
}

// ====================================================================
// 字符串截取和分割
// ====================================================================

/**
 * 安全截取字符串（按字节长度）
 * @param str 字符串
 * @param maxByteLength 最大字节长度
 * @param suffix 后缀
 */
export function truncateByBytes(str: string, maxByteLength: number, suffix: string = '...'): string {
    if (getByteLength(str) <= maxByteLength) {
        return str;
    }
    
    let length = 0;
    let result = '';
    
    for (let i = 0; i < str.length; i++) {
        const char = str.charAt(i);
        const charByteLength = str.charCodeAt(i) > 255 ? 2 : 1;
        
        if (length + charByteLength > maxByteLength) {
            break;
        }
        
        result += char;
        length += charByteLength;
    }
    
    return result + suffix;
}

/**
 * 按字符数截取字符串
 * @param str 字符串
 * @param maxLength 最大长度
 * @param suffix 后缀
 */
export function truncateByChars(str: string, maxLength: number, suffix: string = '...'): string {
    if (str.length <= maxLength) {
        return str;
    }
    return str.substring(0, maxLength) + suffix;
}

/**
 * 智能分割字符串
 * @param str 字符串
 * @param maxLength 每段最大长度
 */
export function smartSplit(str: string, maxLength: number): string[] {
    const result: string[] = [];
    let current = '';
    
    for (let i = 0; i < str.length; i++) {
        const char = str.charAt(i);
        
        if (current.length >= maxLength) {
            result.push(current);
            current = char;
        } else {
            current += char;
        }
    }
    
    if (current) {
        result.push(current);
    }
    
    return result;
}

// ====================================================================
// 随机字符串生成
// ====================================================================

/**
 * 生成随机字符串
 * @param length 长度
 * @param charset 字符集
 */
export function generateRandomString(
    length: number,
    charset: string = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
): string {
    let result = '';
    for (let i = 0; i < length; i++) {
        result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return result;
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 */
export function generateUniqueId(prefix: string = 'id'): string {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 生成UUID
 */
export function generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// ====================================================================
// URL和查询参数处理
// ====================================================================

// parseUrlParams function has been moved to navigation.ts to avoid duplication
// Use parseUrlParams from '@/utils/navigation' instead

/**
 * 构建查询参数字符串
 * @param params 参数对象
 */
export function buildQueryString(params: Record<string, any>): string {
    const pairs: string[] = [];
    
    Object.keys(params).forEach(key => {
        const value = params[key];
        if (value !== null && value !== undefined) {
            pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
        }
    });
    
    return pairs.join('&');
}

/**
 * 提取域名
 * @param url URL地址
 */
export function extractDomain(url: string): string {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname;
    } catch {
        return '';
    }
}