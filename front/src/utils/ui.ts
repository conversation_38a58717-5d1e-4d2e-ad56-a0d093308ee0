/**
 * UI 工具函数
 * 封装常用的toast、loading、确认对话框等UI交互
 */

// ====================================================================
// Toast 相关
// ====================================================================

/**
 * 显示成功提示
 * @param title 提示文本
 * @param duration 持续时间（毫秒）
 */
export function showSuccessToast(title: string, duration: number = 2000) {
  uni.showToast({
    title,
    icon: 'success',
    duration,
    mask: true,
  });
}

/**
 * 显示错误提示
 * @param title 提示文本
 * @param duration 持续时间（毫秒）
 */
export function showErrorToast(title: string, duration: number = 2000) {
  uni.showToast({
    title,
    icon: 'error',
    duration,
    mask: true,
  });
}

/**
 * 显示普通提示
 * @param title 提示文本
 * @param duration 持续时间（毫秒）
 */
export function showToast(title: string, duration: number = 2000) {
  uni.showToast({
    title,
    icon: 'none',
    duration,
    mask: true,
  });
}

/**
 * 显示普通提示（兼容旧版本的 toast 函数）
 * @param message 提示消息
 * @param duration 显示时长
 * @deprecated Use showToast instead. This function will be removed in future versions.
 */
export function toast(message: string, duration: number = 2000) {
  showToast(message, duration);
}

// ====================================================================
// Loading 相关
// ====================================================================

/**
 * 显示加载中提示
 * @param title 提示文本
 * @param mask 是否显示透明蒙层
 */
export function showLoading(title: string = '加载中...', mask: boolean = true) {
  uni.showLoading({
    title,
    mask,
  });
}

/**
 * 隐藏加载中提示
 */
export function hideLoading() {
  uni.hideLoading();
}

/**
 * 带有加载状态的异步操作包装器
 * @param asyncFunction 异步函数
 * @param loadingText 加载文本
 * @param successText 成功文本
 * @param errorText 错误文本
 */
export async function withLoading<T>(
  asyncFunction: () => Promise<T>,
  loadingText: string = '加载中...',
  successText?: string,
  errorText?: string
): Promise<T> {
  try {
    showLoading(loadingText);
    const result = await asyncFunction();

    if (successText) {
      showSuccessToast(successText);
    }

    return result;
  } catch (error) {
    const errorMessage = errorText || (error as Error).message || '操作失败';
    showErrorToast(errorMessage);
    throw error;
  } finally {
    hideLoading();
  }
}

// ====================================================================
// 确认对话框相关
// ====================================================================

export interface ConfirmOptions {
  title?: string;
  content: string;
  confirmText?: string;
  cancelText?: string;
  confirmColor?: string;
  cancelColor?: string;
}

/**
 * 显示确认对话框
 * @param options 对话框选项
 * @returns Promise<boolean> 用户是否点击了确认
 */
export function showConfirm(options: ConfirmOptions): Promise<boolean> {
  return new Promise((resolve) => {
    uni.showModal({
      title: options.title || '提示',
      content: options.content,
      confirmText: options.confirmText || '确定',
      cancelText: options.cancelText || '取消',
      confirmColor: options.confirmColor || '#ff6d00',
      cancelColor: options.cancelColor || '#666666',
      success: (res) => {
        resolve(res.confirm);
      },
      fail: () => {
        resolve(false);
      },
    });
  });
}

/**
 * 显示删除确认对话框
 * @param itemName 要删除的项目名称
 * @param onConfirm 确认回调
 */
export async function showDeleteConfirm(
  itemName: string = '该项目',
  onConfirm: () => Promise<void> | void
) {
  const confirmed = await showConfirm({
    title: '确认删除',
    content: `删除${itemName}后无法恢复，确定要删除吗？`,
    confirmText: '删除',
    confirmColor: '#fa3534',
  });

  if (confirmed) {
    await onConfirm();
  }
}

/**
 * 显示操作确认对话框
 * @param action 操作名称
 * @param itemName 操作对象名称
 * @param onConfirm 确认回调
 */
export async function showActionConfirm(
  action: string,
  itemName: string = '该项目',
  onConfirm: () => Promise<void> | void
) {
  const confirmed = await showConfirm({
    title: `确认${action}`,
    content: `确定要${action}${itemName}吗？`,
    confirmText: action,
  });

  if (confirmed) {
    await onConfirm();
  }
}

// ====================================================================
// 操作单选择
// ====================================================================

export interface ActionSheetOption {
  text: string;
  value: string;
  color?: string;
  disabled?: boolean;
}

/**
 * 显示操作菜单
 * @param options 选项列表
 * @param title 标题
 * @returns Promise<string | null> 用户选择的值，取消则返回null
 */
export function showActionSheet(
  options: ActionSheetOption[],
  title?: string
): Promise<string | null> {
  return new Promise((resolve) => {
    const itemList = options.map(option => option.text);

    uni.showActionSheet({
      title,
      itemList,
      success: (res) => {
        const selectedOption = options[res.tapIndex];
        resolve(selectedOption.value);
      },
      fail: () => {
        resolve(null);
      },
    });
  });
}


// ====================================================================
// 复制相关
// ====================================================================

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @param successText 成功提示文本
 */
export function copyToClipboard(text: string, successText: string = '已复制到剪贴板') {
  uni.setClipboardData({
    data: text,
    success: () => {
      showSuccessToast(successText);
    },
    fail: () => {
      showErrorToast('复制失败');
    },
  });
}

// ====================================================================
// 权限相关
// ====================================================================

/**
 * 检查并请求权限
 * @param scope 权限范围
 * @param reason 权限说明
 */
export function requestPermission(scope: string, reason: string): Promise<boolean> {
  return new Promise((resolve) => {
    uni.getSetting({
      success: (res) => {
        if (res.authSetting[scope]) {
          // 已授权
          resolve(true);
        } else {
          // 未授权，请求权限
          uni.authorize({
            scope,
            success: () => {
              resolve(true);
            },
            fail: () => {
              // 权限被拒绝，引导用户到设置页面
              showConfirm({
                title: '权限申请',
                content: reason,
                confirmText: '去设置',
                cancelText: '取消',
              }).then((confirmed) => {
                if (confirmed) {
                  uni.openSetting({
                    success: (settingRes) => {
                      resolve(!!settingRes.authSetting[scope]);
                    },
                    fail: () => {
                      resolve(false);
                    },
                  });
                } else {
                  resolve(false);
                }
              });
            },
          });
        }
      },
      fail: () => {
        resolve(false);
      },
    });
  });
}

// ====================================================================
// 错误处理
// ====================================================================

/**
 * 统一的错误处理函数
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 */
export function handleError(error: any, defaultMessage: string = '操作失败') {
  console.error('Error:', error);

  let errorMessage = defaultMessage;

  if (error) {
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.errMsg) {
      errorMessage = error.errMsg;
    }
  }

  showErrorToast(errorMessage);
}

/**
 * 安全执行异步操作
 * @param asyncFunction 异步函数
 * @param errorMessage 错误消息
 */
export async function safeExecute<T>(
  asyncFunction: () => Promise<T>,
  errorMessage: string = '操作失败'
): Promise<T | null> {
  try {
    return await asyncFunction();
  } catch (error) {
    handleError(error, errorMessage);
    return null;
  }
}