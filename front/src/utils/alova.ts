import { createAlova } from 'alova'
import VueHook from 'alova/vue'
import UniAppAdapter from '@alova/adapter-uniapp'
import { useUserStore } from '@/stores'
import {
    RESPONSE_CODE,
    HTTP_STATUS,
    RESPONSE_MESSAGE,
    type ApiResponse,
    type ApiError
} from '@/constants/response'

// 获取基础URL
const baseURL = import.meta.env.VITE_SERVER_BASEURL || 'http://localhost:8080/api'

// 创建 Alova 请求实例
export const request = createAlova({
    baseURL,
    timeout: 10000,

    // Vue Hook
    statesHook: VueHook,

    // 使用 uni-app 适配器
    // HTTP2 配置说明：
    // - 生产环境可开启 HTTP2 以提升性能
    // - 开发环境建议关闭，避免调试复杂性
    // - uni-app 在某些平台对 HTTP2 支持有限，需要测试验证
    ...UniAppAdapter({
        // enableHttp2: import.meta.env.PROD ? true : false, // 根据需要调整
    }),

    // 请求拦截器
    beforeRequest: (method) => {
        // 添加默认headers
        method.config.headers = {
            'Content-Type': 'application/json',
            ...method.config.headers,
        }

        // 添加token和设备ID
        try {
            const userStore = useUserStore()
            const token = userStore.accessToken
            if (token) {
                method.config.headers.Authorization = `Bearer ${token}`
            }

            // 添加设备ID头（从本地存储获取）
            const deviceId = uni.getStorageSync('device_id')
            if (deviceId) {
                method.config.headers['X-Device-ID'] = deviceId
            }
        } catch (error) {
            console.warn('获取token或设备ID失败:', error)
        }
    },

    // 响应拦截器
    responded: {
        onSuccess: (response, method) => {
            // 处理 uniapp 响应
            if (typeof response === 'object' && 'data' in response) {
                const { statusCode, data } = response as UniNamespace.RequestSuccessCallbackResult

                // HTTP 状态码检查
                if (statusCode >= 400) {
                    throw new Error(`HTTP Error: ${statusCode}`)
                }

                // 返回统一格式 - 这样 TypeScript 就能正确推断类型
                return data as ApiResponse<any>
            } else {
                // 下载等其他类型响应
                return response
            }
        },

        onError: (error, method) => {
            console.error('请求错误:', error)

            let errorMessage: string = RESPONSE_MESSAGE.NETWORK_ERROR

            // HTTP状态码错误
            if (error.statusCode) {
                const statusCode = error.statusCode
                switch (statusCode) {
                    case HTTP_STATUS.UNAUTHORIZED:
                        // 认证失败 - 清除登录状态，跳转登录页
                        const userStore = useUserStore()
                        userStore.clearUserInfo()
                        uni.navigateTo({ url: '/pages/auth/login' })
                        errorMessage = RESPONSE_MESSAGE.UNAUTHORIZED
                        break
                    case HTTP_STATUS.FORBIDDEN:
                        errorMessage = RESPONSE_MESSAGE.FORBIDDEN
                        break
                    case HTTP_STATUS.NOT_FOUND:
                        errorMessage = RESPONSE_MESSAGE.NOT_FOUND
                        break
                    case HTTP_STATUS.BAD_REQUEST:
                        errorMessage = RESPONSE_MESSAGE.BAD_REQUEST
                        break
                    case HTTP_STATUS.METHOD_NOT_ALLOWED:
                        errorMessage = RESPONSE_MESSAGE.METHOD_NOT_ALLOWED
                        break
                    case HTTP_STATUS.TOO_MANY_REQUESTS:
                        errorMessage = RESPONSE_MESSAGE.TOO_MANY_REQUESTS
                        break
                    case HTTP_STATUS.INTERNAL_SERVER_ERROR:
                        errorMessage = RESPONSE_MESSAGE.SERVER_ERROR
                        break
                    case HTTP_STATUS.SERVICE_UNAVAILABLE:
                        errorMessage = RESPONSE_MESSAGE.SERVICE_UNAVAILABLE
                        break
                    default:
                        errorMessage = `${RESPONSE_MESSAGE.NETWORK_ERROR} (${statusCode})`
                }
            }
            // 业务错误
            else if (error.name === 'BusinessError') {
                errorMessage = error.message
            }
            // 网络错误或其他错误
            else {
                errorMessage = RESPONSE_MESSAGE.NETWORK_ERROR
            }

            // 统一显示错误提示
            uni.showToast({
                icon: 'none',
                title: errorMessage,
            })

            throw error
        }
    },

    // 缓存配置
    cacheFor: {
        GET: 5 * 60 * 1000, // GET请求缓存5分钟
        POST: 0, // POST请求不缓存
        PUT: 0,
        DELETE: 0,
    },

    // 请求共享配置
    shareRequest: true,
})

// 导出便捷方法
export default request 