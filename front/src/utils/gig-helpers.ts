/**
 * 零工模块通用工具函数
 * 统一格式化、验证、状态处理逻辑
 */

import { GigStatus, GigApplicationStatus } from '@/constants/gig';
import type { Gig, GigApplication } from '@/types/gig';

// ==================== 格式化工具 ====================

/**
 * 格式化薪资显示
 */
export function formatGigSalary(salary: number, unit: number): string {
  if (!salary || salary <= 0) return '面议';
  
  const unitMap = {
    1: '元/小时',
    2: '元/天',
    3: '元/月',
    4: '元/次'
  };
  
  const unitText = unitMap[unit as keyof typeof unitMap] || '元';
  
  if (salary >= 10000) {
    return `${(salary / 10000).toFixed(1)}万${unitText}`;
  }
  
  return `${salary}${unitText}`;
}

/**
 * 格式化工作时长
 */
export function formatGigDuration(startTime?: string, endTime?: string): string {
  if (!startTime || !endTime) return '时长待定';
  
  const start = new Date(startTime);
  const end = new Date(endTime);
  const diffMs = end.getTime() - start.getTime();
  const diffHours = Math.round(diffMs / (1000 * 60 * 60));
  
  if (diffHours < 24) {
    return `${diffHours}小时`;
  } else {
    const days = Math.round(diffHours / 24);
    return `${days}天`;
  }
}

/**
 * 格式化工作时间范围
 */
export function formatGigTimeRange(startTime?: string, endTime?: string, format: 'simple' | 'detailed' = 'simple'): string {
  if (!startTime) return '时间待定';
  
  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : null;
  
  if (format === 'simple') {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    if (start.toDateString() === today.toDateString()) {
      return `今天 ${start.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
    } else if (start.toDateString() === tomorrow.toDateString()) {
      return `明天 ${start.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return start.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
    }
  } else {
    const startStr = start.toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    
    if (end) {
      const endStr = end.toLocaleString('zh-CN', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      return `${startStr} - ${endStr}`;
    }
    
    return startStr;
  }
}

/**
 * 格式化申请人数
 */
export function formatApplicantCount(current: number, total: number): string {
  if (total <= 0) return '人数不限';
  return `${current}/${total}人`;
}

/**
 * 格式化相对时间
 */
export function formatGigRelativeTime(dateStr: string): string {
  const date = new Date(dateStr);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffMinutes < 1) return '刚刚';
  if (diffMinutes < 60) return `${diffMinutes}分钟前`;
  if (diffHours < 24) return `${diffHours}小时前`;
  if (diffDays < 7) return `${diffDays}天前`;
  
  return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
}

// ==================== 状态处理 ====================

/**
 * 获取零工状态信息
 */
export function getGigStatusInfo(status: GigStatus) {
  const statusMap = {
    [GigStatus.Draft]: {
      text: '草稿',
      class: 'gig-status--draft',
      color: '#9ca3af',
      bgColor: '#f3f4f6'
    },
    [GigStatus.Recruiting]: {
      text: '招募中',
      class: 'gig-status--recruiting',
      color: '#22c55e',
      bgColor: '#dcfce7'
    },
    [GigStatus.Paused]: {
      text: '暂停',
      class: 'gig-status--paused',
      color: '#eab308',
      bgColor: '#fef3c7'
    },
    [GigStatus.Locked]: {
      text: '已满员',
      class: 'gig-status--locked',
      color: '#3b82f6',
      bgColor: '#dbeafe'
    },
    [GigStatus.InProgress]: {
      text: '进行中',
      class: 'gig-status--progress',
      color: '#3b82f6',
      bgColor: '#dbeafe'
    },
    [GigStatus.Completed]: {
      text: '已完成',
      class: 'gig-status--completed',
      color: '#ff6d00',
      bgColor: '#fff3e0'
    },
    [GigStatus.Closed]: {
      text: '已关闭',
      class: 'gig-status--closed',
      color: '#6b7280',
      bgColor: '#f3f4f6'
    }
  };
  
  return statusMap[status] || statusMap[GigStatus.Draft];
}

/**
 * 获取申请状态信息
 */
export function getApplicationStatusInfo(status: GigApplicationStatus) {
  const statusMap = {
    [GigApplicationStatus.Pending]: {
      text: '待审核',
      class: 'gig-status--pending',
      color: '#eab308',
      bgColor: '#fef3c7'
    },
    [GigApplicationStatus.Confirmed]: {
      text: '已录用',
      class: 'gig-status--confirmed',
      color: '#22c55e',
      bgColor: '#dcfce7'
    },
    [GigApplicationStatus.Rejected]: {
      text: '已拒绝',
      class: 'gig-status--rejected',
      color: '#f52c37',
      bgColor: '#fef2f2'
    },
    [GigApplicationStatus.Withdrawn]: {
      text: '已撤回',
      class: 'gig-status--withdrawn',
      color: '#6b7280',
      bgColor: '#f3f4f6'
    }
  };
  
  return statusMap[status] || statusMap[GigApplicationStatus.Pending];
}

// ==================== 验证工具 ====================

/**
 * 检查零工是否可以申请
 */
export function canApplyGig(gig: Gig): boolean {
  if (!gig) return false;
  if (gig.has_applied) return false;
  if (gig.status !== GigStatus.Recruiting) return false;
  if (gig.people_count > 0 && gig.current_people_count >= gig.people_count) return false;
  
  return true;
}

/**
 * 检查零工是否已满员
 */
export function isGigFull(gig: Gig): boolean {
  if (!gig || gig.people_count <= 0) return false;
  return gig.current_people_count >= gig.people_count;
}

/**
 * 检查零工是否紧急
 */
export function isGigUrgent(gig: Gig): boolean {
  if (!gig) return false;
  if (gig.is_urgent) return true;
  
  // 如果开始时间在24小时内，也认为是紧急的
  if (gig.start_time) {
    const startTime = new Date(gig.start_time);
    const now = new Date();
    const diffHours = (startTime.getTime() - now.getTime()) / (1000 * 60 * 60);
    return diffHours <= 24 && diffHours > 0;
  }
  
  return false;
}

// ==================== 排序工具 ====================

/**
 * 零工排序比较函数
 */
export function compareGigs(a: Gig, b: Gig, sortBy: 'time' | 'salary' | 'urgency' = 'time'): number {
  switch (sortBy) {
    case 'salary':
      return (b.salary || 0) - (a.salary || 0);
    
    case 'urgency':
      const aUrgent = isGigUrgent(a) ? 1 : 0;
      const bUrgent = isGigUrgent(b) ? 1 : 0;
      if (aUrgent !== bUrgent) return bUrgent - aUrgent;
      // 如果紧急程度相同，按时间排序
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    
    case 'time':
    default:
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  }
}

// ==================== 筛选工具 ====================

/**
 * 根据状态筛选零工
 */
export function filterGigsByStatus(gigs: Gig[], status?: GigStatus): Gig[] {
  if (!status) return gigs;
  return gigs.filter(gig => gig.status === status);
}

/**
 * 根据关键词搜索零工
 */
export function searchGigs(gigs: Gig[], keyword: string): Gig[] {
  if (!keyword.trim()) return gigs;
  
  const lowerKeyword = keyword.toLowerCase();
  return gigs.filter(gig => 
    gig.title?.toLowerCase().includes(lowerKeyword) ||
    gig.description?.toLowerCase().includes(lowerKeyword) ||
    gig.address_name?.toLowerCase().includes(lowerKeyword) ||
    gig.tags?.some(tag => tag.toLowerCase().includes(lowerKeyword))
  );
}

// ==================== 统计工具 ====================

/**
 * 计算零工统计信息
 */
export function calculateGigStats(gigs: Gig[]) {
  const stats = {
    total: gigs.length,
    recruiting: 0,
    inProgress: 0,
    completed: 0,
    closed: 0,
    totalSalary: 0,
    avgSalary: 0
  };
  
  gigs.forEach(gig => {
    switch (gig.status) {
      case GigStatus.Recruiting:
        stats.recruiting++;
        break;
      case GigStatus.InProgress:
        stats.inProgress++;
        break;
      case GigStatus.Completed:
        stats.completed++;
        break;
      case GigStatus.Closed:
        stats.closed++;
        break;
    }
    
    if (gig.salary) {
      stats.totalSalary += gig.salary;
    }
  });
  
  stats.avgSalary = stats.total > 0 ? Math.round(stats.totalSalary / stats.total) : 0;
  
  return stats;
}
