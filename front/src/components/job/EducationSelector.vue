<template>
  <view class="education-selector">
    <view class="form-item border-bottom pb-20rpx">
      <view class="form-label text-info mb-20rpx">
        <text class="text-28rpx">最低学历</text>
      </view>
      <view class="education-picker">
        <picker
          :value="educationIndex"
          :range="educationList"
          range-key="name"
          @change="handleEducationChange"
        >
          <view class="form-selector flex justify-between items-center">
            <text
              class="text-30rpx"
              :class="educationText ? 'text-base' : 'text-grey'"
            >
              {{ educationText || "请选择最低学历" }}
            </text>
            <text class="i-carbon-chevron-right text-grey"></text>
          </view>
        </picker>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { educationOptions } from "@/constants/static/job";

const props = defineProps({
  educationId: {
    type: Number,
    default: 0,
  },
  educationText: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:educationId", "update:educationText"]);

// 学历选项列表
const educationList = educationOptions;

// 当前选择的索引
const educationIndex = computed(() => {
  if (props.educationId === 0) return 0;
  const index = educationList.findIndex(
    (item) => item.id === props.educationId
  );
  return index >= 0 ? index : 0;
});

// 处理学历选择变化
const handleEducationChange = (e: any) => {
  const index = e.detail.value;
  const selected = educationList[index];
  emit("update:educationId", selected.id);
  emit("update:educationText", selected.name);
};

// 监听educationId变化
watch(
  () => props.educationId,
  (val) => {
    if (val === 0) {
      emit("update:educationText", "");
    }
  }
);
</script>

<style lang="scss" scoped>
.form-item {
  margin-bottom: 30rpx;
}

.border-bottom {
  border-bottom: 1rpx solid $border-color;
}

.form-selector {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  background-color: $bg-input;
  border-radius: $radius;
  border: 1rpx solid $border-color;
}
</style>
