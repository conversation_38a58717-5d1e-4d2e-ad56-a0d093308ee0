<template>
  <view class="page-layout" :style="{ backgroundColor: bgColor }">
    <!-- Custom Navigation Bar -->
    <CustomNavBar v-if="showNavBar" :title="title" />

    <!-- Page Content Slot -->
    <view class="page-content">
      <slot></slot>
    </view>

    <!-- Global Privacy Popup -->
    <PrivacyPopup
      :show="globalStore.isPrivacyPopupVisible"
      @agree="handleAgreePrivacy"
      @directLogin="handleDirectLogin"
      @reject="handleRejectPrivacy"
      @close="handleClosePrivacy"
    />
    
    <!-- Login Tip Bar -->
    <LoginTip
      v-if="showLoginTip"
      :is-fixed="true"
      :closable="true"
      @login="handleLoginTip"
      @close="handleCloseLoginTip"
    />
  </view>
</template>

<script setup lang="ts">
import { useGlobalStore } from "@/stores/global";
import CustomNavBar from "@/components/CustomNavBar.vue";
import PrivacyPopup from "@/components/common/PrivacyPopup.vue";
import LoginTip from "@/components/common/LoginTip.vue";
import { wechatLogin } from "@/api/auth";
import { useUserStore } from "@/stores/user";

// Props for customization
defineProps({
  showNavBar: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: "",
  },
  bgColor: {
    type: String,
    default: "var(--bg-page)",
  },
  showLoginTip: {
    type: Boolean,
    default: true, // 默认显示登录提示条
  },
});

onLoad(() => {
  uni.$on("onLogin", () => {
    globalStore.showPrivacyPopup();
  });
});

const globalStore = useGlobalStore();
const userStore = useUserStore();

const handleAgreePrivacy = async (data: any) => {
  // 处理新用户注册成功的回调
  userStore.setUserInfo(data.user, data.access_token);
};

const handleDirectLogin = async (data: any) => {
  // 处理已注册用户直接登录的回调
  userStore.setUserInfo(data.user, data.access_token);
};

const handleRejectPrivacy = () => {
  // 根据业务需求处理拒绝逻辑，例如退出小程序
  uni.showModal({
    title: "提示",
    content: "您需要同意隐私协议才能继续使用",
    showCancel: false,
  });
};

const handleClosePrivacy = () => {
  // 点击遮罩层关闭，也视为临时拒绝
  handleRejectPrivacy();
};

const handleLoginTip = () => {
  // 处理登录提示条点击
  globalStore.showPrivacyPopup();
};

const handleCloseLoginTip = () => {
  // 处理登录提示条关闭
  console.log('登录提示条已关闭');
};
</script>

<style scoped>
.page-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  position: relative;
}
</style>
